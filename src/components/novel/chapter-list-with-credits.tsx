"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { 
  Play, 
  Lock, 
  Coins, 
  Crown, 
  CheckCircle,
  AlertTriangle
} from "lucide-react"
import { formatDate } from "@/lib/utils"
import { formatCredits } from "@/lib/credits"
import { useGetCreditBalanceQuery } from "@/store/api/creditsApi"

interface Chapter {
  id: string
  title: string
  order: number
  createdAt: string | Date
  isPremium?: boolean
  creditPrice?: number | null
  requiredTier?: string | null
}

interface ChapterWithPricing extends Chapter {
  isPremium: boolean
  creditPrice?: number | null
  requiredTier?: string | null
}

interface ChapterListWithCreditsProps {
  novelId: string
  chapters: Chapter[]
  className?: string
}

export function ChapterListWithCredits({ 
  novelId, 
  chapters, 
  className 
}: ChapterListWithCreditsProps) {
  const { data: session } = useSession()
  const [chaptersWithPricing, setChaptersWithPricing] = useState<ChapterWithPricing[]>([])
  const [isLoadingPricing, setIsLoadingPricing] = useState(true)

  // Get user's credit balance
  const { data: balanceData } = useGetCreditBalanceQuery(undefined, {
    skip: !session?.user,
  })

  const userBalance = balanceData?.balance ?? 0

  // Fetch pricing information for all chapters
  useEffect(() => {
    const fetchChapterPricing = async () => {
      if (chapters.length === 0) {
        setIsLoadingPricing(false)
        return
      }

      try {
        setIsLoadingPricing(true)
        
        // Fetch pricing for each chapter
        const pricingPromises = chapters.map(async (chapter) => {
          try {
            const response = await fetch(`/api/chapters/${chapter.id}/pricing`)
            if (response.ok) {
              const pricingData = await response.json()
              return {
                ...chapter,
                isPremium: pricingData.isPremium || false,
                creditPrice: pricingData.creditPrice,
                requiredTier: pricingData.requiredTier,
              }
            }
          } catch (error) {
            console.error(`Error fetching pricing for chapter ${chapter.id}:`, error)
          }
          
          // Fallback to basic chapter data
          return {
            ...chapter,
            isPremium: chapter.isPremium || false,
            creditPrice: chapter.creditPrice,
            requiredTier: chapter.requiredTier,
          }
        })

        const chaptersWithPricingData = await Promise.all(pricingPromises)
        setChaptersWithPricing(chaptersWithPricingData)
      } catch (error) {
        console.error('Error fetching chapter pricing:', error)
        // Fallback to original chapters
        setChaptersWithPricing(chapters.map(chapter => ({
          ...chapter,
          isPremium: chapter.isPremium || false,
          creditPrice: chapter.creditPrice,
          requiredTier: chapter.requiredTier,
        })))
      } finally {
        setIsLoadingPricing(false)
      }
    }

    fetchChapterPricing()
  }, [chapters])

  const getChapterAccessIcon = (chapter: ChapterWithPricing) => {
    if (!chapter.isPremium) {
      return <Play className="h-4 w-4 text-green-500" />
    }

    if (chapter.creditPrice && chapter.creditPrice > 0) {
      const canAfford = userBalance >= chapter.creditPrice
      return canAfford ? 
        <Coins className="h-4 w-4 text-yellow-500" /> : 
        <Lock className="h-4 w-4 text-red-500" />
    }

    return <Crown className="h-4 w-4 text-purple-500" />
  }

  const getAccessBadge = (chapter: ChapterWithPricing) => {
    if (!chapter.isPremium) {
      return <Badge variant="secondary" className="text-green-600">Free</Badge>
    }

    if (chapter.creditPrice && chapter.creditPrice > 0) {
      const canAfford = userBalance >= chapter.creditPrice
      return (
        <Badge variant={canAfford ? "secondary" : "destructive"}>
          {formatCredits(chapter.creditPrice)}
        </Badge>
      )
    }

    if (chapter.requiredTier) {
      return <Badge variant="outline">{chapter.requiredTier}</Badge>
    }

    return <Badge variant="outline">Premium</Badge>
  }

  if (chapters.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Chapters</CardTitle>
          <CardDescription>No chapters available yet</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Chapters ({chapters.length})</CardTitle>
        <CardDescription>
          Click on any chapter to start reading
          {session?.user && (
            <span className="ml-2 text-sm">
              • Your balance: <strong>{formatCredits(userBalance)}</strong>
            </span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {isLoadingPricing ? (
            // Loading skeletons
            chapters.map((chapter) => (
              <div key={chapter.id} className="p-4 rounded-lg border">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <Skeleton className="h-5 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-1/2" />
                  </div>
                  <Skeleton className="h-6 w-16" />
                </div>
              </div>
            ))
          ) : (
            chaptersWithPricing.map((chapter) => {
              const canAccess = !chapter.isPremium ||
                               (chapter.creditPrice && userBalance >= chapter.creditPrice) ||
                               !chapter.creditPrice; // Free or subscription-based

              const ChapterContent = (
                <div className={`p-4 rounded-lg border transition-colors ${
                  canAccess
                    ? 'hover:bg-muted/50 cursor-pointer'
                    : 'bg-muted/30 cursor-not-allowed opacity-75'
                }`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3 flex-1">
                      {getChapterAccessIcon(chapter)}
                      <div>
                        <h4 className={`font-medium ${!canAccess ? 'text-muted-foreground' : ''}`}>
                          Chapter {chapter.order}: {chapter.title}
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          {formatDate(chapter.createdAt)}
                          {!canAccess && chapter.creditPrice && (
                            <span className="ml-2 text-red-600 font-medium">
                              • Need {formatCredits(chapter.creditPrice - userBalance)} more
                            </span>
                          )}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {getAccessBadge(chapter)}
                      {!canAccess && chapter.isPremium && chapter.creditPrice && (
                        <AlertTriangle className="h-4 w-4 text-red-500" title="Insufficient credits" />
                      )}
                    </div>
                  </div>
                </div>
              );

              return canAccess ? (
                <Link
                  key={chapter.id}
                  href={`/novels/${novelId}/chapters/${chapter.id}`}
                  className="block"
                >
                  {ChapterContent}
                </Link>
              ) : (
                <div key={chapter.id} className="block">
                  {ChapterContent}
                </div>
              );
            })
          )}
        </div>
      </CardContent>
    </Card>
  )
}
