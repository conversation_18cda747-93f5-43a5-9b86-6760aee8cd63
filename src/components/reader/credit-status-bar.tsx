"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Coins, 
  Plus, 
  TrendingDown, 
  TrendingUp,
  Info,
  AlertTriangle
} from "lucide-react"
import { formatCredits } from "@/lib/credits"
import { CreditPurchaseModal } from "@/components/credits/credit-purchase-modal"
import { useGetCreditBalanceQuery } from "@/store/api/creditsApi"

interface CreditStatusBarProps {
  className?: string
  showRecommendations?: boolean
  minimumRecommended?: number
}

export function CreditStatusBar({
  className,
  showRecommendations = true,
  minimumRecommended = 10
}: CreditStatusBarProps) {
  const { data: session } = useSession()
  const [showPurchaseModal, setShowPurchaseModal] = useState(false)

  // Get current credit balance
  const { 
    data: balanceData, 
    isLoading: isLoadingBalance,
    refetch: refetchBalance 
  } = useGetCreditBalanceQuery(undefined, {
    skip: !session?.user,
    pollingInterval: 30000, // Poll every 30 seconds
  })

  const currentBalance = balanceData?.balance ?? 0

  // Don't show for non-authenticated users or authors
  if (!session?.user || session.user.role === "AUTHOR") {
    return null
  }

  const isLowBalance = currentBalance < minimumRecommended
  const isVeryLowBalance = currentBalance < 5

  return (
    <>
      <Card className={`border-l-4 ${isVeryLowBalance ? 'border-l-red-500 bg-red-50' : isLowBalance ? 'border-l-yellow-500 bg-yellow-50' : 'border-l-green-500 bg-green-50'} ${className}`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-full ${isVeryLowBalance ? 'bg-red-100' : isLowBalance ? 'bg-yellow-100' : 'bg-green-100'}`}>
                <Coins className={`h-5 w-5 ${isVeryLowBalance ? 'text-red-600' : isLowBalance ? 'text-yellow-600' : 'text-green-600'}`} />
              </div>
              
              <div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">Your Credits:</span>
                  {isLoadingBalance ? (
                    <Skeleton className="h-6 w-16" />
                  ) : (
                    <Badge 
                      variant={isVeryLowBalance ? "destructive" : isLowBalance ? "secondary" : "default"}
                      className="text-lg font-bold px-3 py-1"
                    >
                      {formatCredits(currentBalance)}
                    </Badge>
                  )}
                </div>
                
                {showRecommendations && !isLoadingBalance && (
                  <div className="text-sm text-muted-foreground mt-1">
                    {isVeryLowBalance ? (
                      <div className="flex items-center gap-1 text-red-600">
                        <AlertTriangle className="h-3 w-3" />
                        <span>Very low balance - consider topping up</span>
                      </div>
                    ) : isLowBalance ? (
                      <div className="flex items-center gap-1 text-yellow-600">
                        <TrendingDown className="h-3 w-3" />
                        <span>Low balance - you may want to add more credits</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-1 text-green-600">
                        <TrendingUp className="h-3 w-3" />
                        <span>Good balance for reading premium content</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button 
                onClick={() => setShowPurchaseModal(true)}
                size="sm"
                variant={isVeryLowBalance ? "default" : "outline"}
                className={isVeryLowBalance ? "bg-red-600 hover:bg-red-700" : ""}
              >
                <Plus className="h-4 w-4 mr-2" />
                {isVeryLowBalance ? "Top Up Now" : "Buy Credits"}
              </Button>
            </div>
          </div>

          {/* Additional info for very low balance */}
          {isVeryLowBalance && (
            <Alert className="mt-3 border-red-200 bg-red-50">
              <Info className="h-4 w-4" />
              <AlertDescription className="text-red-800">
                Most premium chapters cost 3-10 credits. Consider purchasing more to continue reading.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Credit Purchase Modal */}
      {showPurchaseModal && (
        <CreditPurchaseModal
          isOpen={showPurchaseModal}
          onClose={() => setShowPurchaseModal(false)}
          onSuccess={() => {
            refetchBalance()
            setShowPurchaseModal(false)
          }}
        />
      )}
    </>
  )
}
