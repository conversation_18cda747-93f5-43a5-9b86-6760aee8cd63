"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Play, 
  Lock, 
  Coins, 
  Alert<PERSON>riangle,
  Crown
} from "lucide-react"
import { formatCredits } from "@/lib/credits"
import { useGetCreditBalanceQuery } from "@/store/api/creditsApi"

interface ReadChapterButtonProps {
  novelId: string
  chapterId: string
  chapterTitle: string
  chapterOrder: number
  chapterStatus: string
  isAuthor?: boolean
  className?: string
}

interface ChapterPricing {
  isPremium: boolean
  creditPrice?: number | null
  requiredTier?: string | null
}

export function ReadChapterButton({
  novelId,
  chapterId,
  chapterTitle,
  chapterOrder,
  chapterStatus,
  isAuthor = false,
  className
}: ReadChapterButtonProps) {
  const { data: session } = useSession()
  const [pricing, setPricing] = useState<ChapterPricing | null>(null)
  const [isLoadingPricing, setIsLoadingPricing] = useState(true)

  // Get user's credit balance
  const { data: balanceData } = useGetCreditBalanceQuery(undefined, {
    skip: !session?.user || isAuthor,
  })

  const userBalance = balanceData?.balance ?? 0

  // Fetch chapter pricing
  useEffect(() => {
    const fetchPricing = async () => {
      if (isAuthor || chapterStatus !== 'PUBLISHED') {
        setIsLoadingPricing(false)
        return
      }

      try {
        const response = await fetch(`/api/chapters/${chapterId}/pricing`)
        if (response.ok) {
          const data = await response.json()
          setPricing({
            isPremium: data.isPremium || false,
            creditPrice: data.creditPrice,
            requiredTier: data.requiredTier,
          })
        }
      } catch (error) {
        console.error('Error fetching chapter pricing:', error)
      } finally {
        setIsLoadingPricing(false)
      }
    }

    fetchPricing()
  }, [chapterId, isAuthor, chapterStatus])

  // If chapter is not published
  if (chapterStatus !== 'PUBLISHED') {
    if (isAuthor) {
      return (
        <Link href={`/dashboard/novels/${novelId}/chapters/${chapterId}/edit`}>
          <Button size="sm" variant="outline" className={className}>
            <Play className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </Link>
      )
    } else {
      return (
        <Button size="sm" variant="outline" disabled className={className}>
          <Lock className="h-4 w-4 mr-2" />
          Locked
        </Button>
      )
    }
  }

  // If user is author, always allow access
  if (isAuthor) {
    return (
      <Link href={`/novels/${novelId}/chapters/${chapterId}`}>
        <Button size="sm" className={className}>
          <Play className="h-4 w-4 mr-2" />
          Read
        </Button>
      </Link>
    )
  }

  // If still loading pricing
  if (isLoadingPricing) {
    return (
      <Button size="sm" disabled className={className}>
        <Play className="h-4 w-4 mr-2" />
        Loading...
      </Button>
    )
  }

  // If chapter is free or user is not authenticated
  if (!pricing?.isPremium || !session?.user) {
    return (
      <Link href={`/novels/${novelId}/chapters/${chapterId}`}>
        <Button size="sm" className={className}>
          <Play className="h-4 w-4 mr-2" />
          Read
        </Button>
      </Link>
    )
  }

  // Chapter is premium - check access
  const creditPrice = pricing.creditPrice || 0
  const canAfford = creditPrice > 0 ? userBalance >= creditPrice : true
  const needsSubscription = !creditPrice && pricing.requiredTier

  // If user can't afford the chapter
  if (creditPrice > 0 && !canAfford) {
    const shortfall = creditPrice - userBalance
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Button size="sm" variant="outline" disabled>
          <Lock className="h-4 w-4 mr-2" />
          {formatCredits(creditPrice)}
        </Button>
        <div className="flex items-center gap-1 text-red-600 text-xs">
          <AlertTriangle className="h-3 w-3" />
          <span>Need {formatCredits(shortfall)} more</span>
        </div>
      </div>
    )
  }

  // If chapter requires subscription
  if (needsSubscription) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Button size="sm" variant="outline" disabled>
          <Crown className="h-4 w-4 mr-2" />
          {pricing.requiredTier}
        </Button>
        <div className="text-xs text-muted-foreground">
          Subscription required
        </div>
      </div>
    )
  }

  // User can access the chapter
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Link href={`/novels/${novelId}/chapters/${chapterId}`}>
        <Button size="sm">
          <Play className="h-4 w-4 mr-2" />
          Read
        </Button>
      </Link>
      {creditPrice > 0 && (
        <Badge variant="secondary" className="text-xs">
          {formatCredits(creditPrice)}
        </Badge>
      )}
    </div>
  )
}
