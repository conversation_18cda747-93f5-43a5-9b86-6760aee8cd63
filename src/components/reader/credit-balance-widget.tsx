"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Coins, Plus, Info } from "lucide-react"
import { formatCredits } from "@/lib/credits"
import { CreditPurchaseModal } from "@/components/credits/credit-purchase-modal"
import { useGetCreditBalanceQuery } from "@/store/api/creditsApi"

interface CreditBalanceWidgetProps {
  className?: string
  showPurchaseButton?: boolean
  compact?: boolean
}

export function CreditBalanceWidget({
  className,
  showPurchaseButton = true,
  compact = false
}: CreditBalanceWidgetProps) {
  const { data: session } = useSession()
  const [showPurchaseModal, setShowPurchaseModal] = useState(false)

  // Get current credit balance
  const { 
    data: balanceData, 
    isLoading: isLoadingBalance,
    refetch: refetchBalance 
  } = useGetCreditBalanceQuery(undefined, {
    skip: !session?.user,
    pollingInterval: 30000, // Poll every 30 seconds
  })

  const currentBalance = balanceData?.balance ?? 0

  // If user is not authenticated, show sign-in prompt
  if (!session?.user) {
    if (compact) return null
    
    return (
      <Card className={`border-blue-200 bg-blue-50 ${className}`}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <Info className="h-5 w-5" />
            Sign In Required
          </CardTitle>
          <CardDescription className="text-blue-600">
            Sign in to view your credit balance
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  if (compact) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Coins className="h-4 w-4 text-yellow-500" />
        <span className="text-sm font-medium">Credits:</span>
        {isLoadingBalance ? (
          <Skeleton className="h-5 w-12" />
        ) : (
          <Badge variant="secondary" className="font-bold">
            {formatCredits(currentBalance)}
          </Badge>
        )}
        {showPurchaseButton && (
          <Button 
            onClick={() => setShowPurchaseModal(true)}
            size="sm"
            variant="outline"
            className="h-6 px-2 text-xs"
          >
            <Plus className="h-3 w-3" />
          </Button>
        )}
      </div>
    )
  }

  return (
    <>
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <Coins className="h-5 w-5 text-yellow-500" />
            Your Credits
          </CardTitle>
          <CardDescription>
            Use credits to unlock premium chapters
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Balance */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Current Balance:</span>
            {isLoadingBalance ? (
              <Skeleton className="h-6 w-16" />
            ) : (
              <Badge variant="secondary" className="text-lg font-bold px-3 py-1">
                {formatCredits(currentBalance)}
              </Badge>
            )}
          </div>

          {/* Top Up Button */}
          {showPurchaseButton && (
            <div className="pt-2 border-t">
              <Button 
                onClick={() => setShowPurchaseModal(true)}
                variant="outline" 
                size="sm"
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                Buy More Credits
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Credit Purchase Modal */}
      {showPurchaseModal && (
        <CreditPurchaseModal
          isOpen={showPurchaseModal}
          onClose={() => setShowPurchaseModal(false)}
          onSuccess={() => {
            refetchBalance()
            setShowPurchaseModal(false)
          }}
        />
      )}
    </>
  )
}
