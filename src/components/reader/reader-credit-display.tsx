"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import { 
  Coins, 
  Plus, 
  Lock, 
  Crown, 
  ShoppingCart, 
  AlertTriangle,
  Info,
  Zap
} from "lucide-react"
import { formatCredits } from "@/lib/credits"
import { CreditPurchaseModal } from "@/components/credits/credit-purchase-modal"
import { useGetCreditBalanceQuery, useSpendCreditsMutation } from "@/store/api/creditsApi"
import { useToast } from "@/hooks/use-toast"

interface ReaderCreditDisplayProps {
  chapterInfo?: {
    id: string
    title: string
    order: number
    isPremium: boolean
    creditPrice?: number | null
    requiredTier?: string | null
  }
  accessInfo?: {
    hasAccess: boolean
    reason?: string
    creditPrice?: number | null
    canPurchaseWithCredits?: boolean
    userCreditBalance?: number
    hasPurchased?: boolean
    purchaseOptions?: {
      credits: {
        available: boolean
        price: number
        canAfford: boolean
        userBalance: number
      }
    }
  }
  onAccessGranted?: () => void
  className?: string
}

export function ReaderCreditDisplay({
  chapterInfo,
  accessInfo,
  onAccessGranted,
  className
}: ReaderCreditDisplayProps) {
  const { data: session } = useSession()
  const { toast } = useToast()
  const [showPurchaseModal, setShowPurchaseModal] = useState(false)
  const [isPurchasing, setIsPurchasing] = useState(false)

  // Get current credit balance
  const { 
    data: balanceData, 
    isLoading: isLoadingBalance,
    refetch: refetchBalance 
  } = useGetCreditBalanceQuery(undefined, {
    skip: !session?.user,
    pollingInterval: 30000, // Poll every 30 seconds
  })

  const [spendCredits] = useSpendCreditsMutation()

  const currentBalance = balanceData?.balance ?? accessInfo?.userCreditBalance ?? 0

  // If user is not authenticated, show sign-in prompt
  if (!session?.user) {
    return (
      <Card className={`border-blue-200 bg-blue-50 ${className}`}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <Info className="h-5 w-5" />
            Sign In Required
          </CardTitle>
          <CardDescription className="text-blue-600">
            Sign in to view your credit balance and unlock premium content
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  const handlePurchaseWithCredits = async () => {
    if (!chapterInfo?.id || !accessInfo?.creditPrice) return

    try {
      setIsPurchasing(true)
      
      const result = await spendCredits({
        contentType: 'CHAPTER',
        contentId: chapterInfo.id,
      }).unwrap()

      toast({
        title: "Chapter Unlocked!",
        description: `You spent ${formatCredits(result.creditsSpent)} to unlock "${chapterInfo.title}"`,
      })

      // Refetch balance and notify parent
      await refetchBalance()
      onAccessGranted?.()

    } catch (error: any) {
      console.error('Error purchasing chapter:', error)
      toast({
        title: "Purchase Failed",
        description: error.data?.error || "Failed to purchase chapter. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsPurchasing(false)
    }
  }

  const needsCredits = chapterInfo?.isPremium && !accessInfo?.hasAccess && accessInfo?.canPurchaseWithCredits
  const canAfford = accessInfo?.purchaseOptions?.credits.canAfford ?? false
  const creditPrice = accessInfo?.creditPrice ?? chapterInfo?.creditPrice ?? 0

  return (
    <>
      <Card className={`${className}`}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <Coins className="h-5 w-5 text-yellow-500" />
            Your Credits
          </CardTitle>
          <CardDescription>
            Use credits to unlock premium chapters
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Balance */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Current Balance:</span>
            {isLoadingBalance ? (
              <Skeleton className="h-6 w-16" />
            ) : (
              <Badge variant="secondary" className="text-lg font-bold px-3 py-1">
                {formatCredits(currentBalance)}
              </Badge>
            )}
          </div>

          {/* Chapter Credit Requirement */}
          {chapterInfo?.isPremium && (
            <div className="p-3 border rounded-lg bg-muted/50">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Lock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">This Chapter</span>
                </div>
                {creditPrice > 0 && (
                  <Badge variant="outline" className="text-yellow-600">
                    {formatCredits(creditPrice)}
                  </Badge>
                )}
              </div>
              
              {accessInfo?.hasAccess ? (
                <div className="flex items-center gap-2 text-green-600">
                  <Zap className="h-4 w-4" />
                  <span className="text-sm">You have access to this chapter</span>
                </div>
              ) : accessInfo?.hasPurchased ? (
                <div className="flex items-center gap-2 text-green-600">
                  <ShoppingCart className="h-4 w-4" />
                  <span className="text-sm">Already purchased</span>
                </div>
              ) : needsCredits ? (
                <div className="space-y-2">
                  {canAfford ? (
                    <Button 
                      onClick={handlePurchaseWithCredits}
                      disabled={isPurchasing}
                      size="sm"
                      className="w-full"
                    >
                      {isPurchasing ? (
                        <>
                          <Zap className="h-4 w-4 mr-2 animate-pulse" />
                          Unlocking...
                        </>
                      ) : (
                        <>
                          <ShoppingCart className="h-4 w-4 mr-2" />
                          Unlock for {formatCredits(creditPrice)}
                        </>
                      )}
                    </Button>
                  ) : (
                    <Alert>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        You need {formatCredits(creditPrice - currentBalance)} more credits
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              ) : (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Crown className="h-4 w-4" />
                  <span className="text-sm">Requires subscription</span>
                </div>
              )}
            </div>
          )}

          {/* Top Up Button */}
          <div className="pt-2 border-t">
            <Button 
              onClick={() => setShowPurchaseModal(true)}
              variant="outline" 
              size="sm"
              className="w-full"
            >
              <Plus className="h-4 w-4 mr-2" />
              Buy More Credits
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Credit Purchase Modal */}
      <CreditPurchaseModal
        isOpen={showPurchaseModal}
        onClose={() => setShowPurchaseModal(false)}
        onSuccess={() => {
          refetchBalance()
          setShowPurchaseModal(false)
        }}
      />
    </>
  )
}
