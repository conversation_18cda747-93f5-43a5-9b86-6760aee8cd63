"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  ArrowRight,
  BookOpen,
  Settings,
  ChevronLeft,
  ChevronRight,
  Type,
  Palette,
  Heart,
  Share,
  Bookmark
} from "lucide-react"
import { formatDate } from "@/lib/utils"
import { useGetChaptersQuery } from "@/store/api/chaptersApi"
import { LibraryButton } from "@/components/library/library-button"
import { ChapterContentRenderer } from "@/components/chapter/chapter-content-renderer"
import { ReaderCreditDisplay } from "@/components/reader/reader-credit-display"
import { ContentPaywall } from "@/components/credits/content-paywall"
import { useChapterAccess } from "@/hooks/use-chapter-access"

interface Chapter {
  id: string
  title: string
  content: string
  order: number
  createdAt: string | Date
  novel?: {
    id: string
    title: string
  }
}

interface ChapterReaderProps {
  chapter: Chapter
  novelId: string
}

export function ChapterReader({ chapter, novelId }: ChapterReaderProps) {
  const [fontSize, setFontSize] = useState(16)
  const [theme, setTheme] = useState<"light" | "dark" | "sepia">("light")
  const [showSettings, setShowSettings] = useState(false)

  // Check chapter access
  const { accessInfo, isLoading: isLoadingAccess, refetch: refetchAccess } = useChapterAccess(chapter.id)

  // Fetch all chapters for navigation
  const { data: chapters } = useGetChaptersQuery(novelId)

  // Find current chapter index and navigation
  const currentChapterIndex = Array.isArray(chapters) ? chapters.findIndex(ch => ch.id === chapter.id) : -1
  const previousChapter = currentChapterIndex > 0 && Array.isArray(chapters) ? chapters[currentChapterIndex - 1] : null
  const nextChapter = currentChapterIndex >= 0 && Array.isArray(chapters) && currentChapterIndex < chapters.length - 1 
    ? chapters[currentChapterIndex + 1] 
    : null

  // Load reading preferences from localStorage
  useEffect(() => {
    const savedFontSize = localStorage.getItem("reader-font-size")
    const savedTheme = localStorage.getItem("reader-theme")

    if (savedFontSize) setFontSize(parseInt(savedFontSize))
    if (savedTheme) setTheme(savedTheme as "light" | "dark" | "sepia")
  }, [])

  // Save preferences to localStorage
  useEffect(() => {
    localStorage.setItem("reader-font-size", fontSize.toString())
    localStorage.setItem("reader-theme", theme)
  }, [fontSize, theme])

  const themeClasses = {
    light: "bg-white text-gray-900",
    dark: "bg-gray-900 text-gray-100",
    sepia: "bg-amber-50 text-amber-900"
  }

  return (
    <div className={`min-h-screen transition-colors ${themeClasses[theme]}`}>
      {/* Header */}
      <header className="sticky top-0 z-50 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" asChild>
                <Link href={`/novels/${novelId}`}>
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Novel
                </Link>
              </Button>

              <div className="hidden md:block">
                <h1 className="font-semibold truncate max-w-md">
                  {chapter.novel?.title || "Novel"}
                </h1>
                <p className="text-sm text-muted-foreground">
                  Chapter {chapter.order}: {chapter.title}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <LibraryButton
                novelId={novelId}
                variant="ghost"
                size="sm"
              />
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSettings(!showSettings)}
              >
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Reading Settings Panel */}
      {showSettings && (
        <div className="border-b bg-muted/50 p-4">
          <div className="container mx-auto max-w-4xl">
            <div className="flex flex-wrap items-center gap-6">
              {/* Font Size */}
              <div className="flex items-center gap-2">
                <Type className="h-4 w-4" />
                <span className="text-sm font-medium">Font Size:</span>
                <div className="flex items-center gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setFontSize(Math.max(12, fontSize - 2))}
                    disabled={fontSize <= 12}
                  >
                    -
                  </Button>
                  <span className="w-8 text-center text-sm">{fontSize}</span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setFontSize(Math.min(24, fontSize + 2))}
                    disabled={fontSize >= 24}
                  >
                    +
                  </Button>
                </div>
              </div>

              {/* Theme */}
              <div className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                <span className="text-sm font-medium">Theme:</span>
                <div className="flex gap-1">
                  {(["light", "dark", "sepia"] as const).map((t) => (
                    <Button
                      key={t}
                      variant={theme === t ? "default" : "outline"}
                      size="sm"
                      onClick={() => setTheme(t)}
                      className="capitalize"
                    >
                      {t}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Chapter Content */}
      <main className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <article className="lg:col-span-3 space-y-8">
            {/* Chapter Header */}
            <header className="text-center space-y-4 pb-8 border-b">
              <div className="space-y-2">
                <Badge variant="outline" className="mb-2">
                  Chapter {chapter.order}
                </Badge>
                <h1 className="text-3xl md:text-4xl font-bold tracking-tight">
                  {chapter.title}
                </h1>
                <p className="text-muted-foreground">
                  Published {formatDate(chapter.createdAt)}
                </p>
              </div>
            </header>

            {/* Access Control */}
            {isLoadingAccess ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="text-muted-foreground mt-2">Checking access...</p>
              </div>
            ) : accessInfo && !accessInfo.hasAccess ? (
              <ContentPaywall
                contentType="CHAPTER"
                contentId={chapter.id}
                contentTitle={`Chapter ${chapter.order}: ${chapter.title}`}
                accessInfo={accessInfo.accessInfo}
                onAccessGranted={refetchAccess}
              />
            ) : (
              <div className="space-y-8">
                {/* Chapter Content */}
                <ChapterContentRenderer
                  content={chapter.content}
                  fontSize={fontSize}
                  className="custom-scrollbar"
                />

                {/* Chapter Navigation */}
                <footer className="pt-8 border-t space-y-4">
            {/* Chapter Progress */}
            {Array.isArray(chapters) && chapters.length > 1 && (
              <div className="text-center">
                <p className="text-sm text-muted-foreground mb-2">
                  Chapter {currentChapterIndex + 1} of {chapters.length}
                </p>
                <div className="w-full bg-muted rounded-full h-2 max-w-xs mx-auto">
                  <div 
                    className="bg-primary h-2 rounded-full transition-all"
                    style={{ width: `${((currentChapterIndex + 1) / chapters.length) * 100}%` }}
                  />
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex items-center justify-between">
              {previousChapter ? (
                <Button variant="outline" asChild>
                  <Link href={`/novels/${novelId}/chapters/${previousChapter.id}`}>
                    <ChevronLeft className="mr-2 h-4 w-4" />
                    <div className="text-left">
                      <div className="text-xs text-muted-foreground">Previous</div>
                      <div className="font-medium">Chapter {previousChapter.order}</div>
                    </div>
                  </Link>
                </Button>
              ) : (
                <Button variant="outline" disabled>
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  <div className="text-left">
                    <div className="text-xs text-muted-foreground">Previous</div>
                    <div className="font-medium">Chapter</div>
                  </div>
                </Button>
              )}

              <Button asChild>
                <Link href={`/novels/${novelId}`}>
                  <BookOpen className="mr-2 h-4 w-4" />
                  Back to Novel
                </Link>
              </Button>

              {nextChapter ? (
                <Button variant="outline" asChild>
                  <Link href={`/novels/${novelId}/chapters/${nextChapter.id}`}>
                    <div className="text-right">
                      <div className="text-xs text-muted-foreground">Next</div>
                      <div className="font-medium">Chapter {nextChapter.order}</div>
                    </div>
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              ) : (
                <Button variant="outline" disabled>
                  <div className="text-right">
                    <div className="text-xs text-muted-foreground">Next</div>
                    <div className="font-medium">Chapter</div>
                  </div>
                  <ChevronRight className="ml-2 h-4 w-4" />
                </Button>
              )}
            </div>
                </footer>
              </div>
            )}
          </article>

          {/* Sidebar */}
          <aside className="lg:col-span-1 space-y-6">
            {/* Credit Display */}
            <ReaderCreditDisplay
              chapterInfo={accessInfo?.chapter}
              accessInfo={accessInfo?.accessInfo}
              onAccessGranted={refetchAccess}
            />
          </aside>
        </div>
      </main>
    </div>
  )
}