"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { 
  Clock, 
  BookOpen, 
  CheckCircle, 
  Play, 
  Pause, 
  RotateCcw,
  Eye,
  Timer
} from "lucide-react"
import { formatDistanceToNow } from "date-fns"

interface ChapterProgressTrackerProps {
  chapterId: string
  novelId: string
  chapterTitle: string
  chapterOrder: number
  totalChapters: number
  estimatedReadingTime?: number // in minutes
  onProgressUpdate?: (progress: number) => void
  onChapterComplete?: () => void
}

export function ChapterProgressTracker({
  chapterId,
  novelId,
  chapterTitle,
  chapterOrder,
  totalChapters,
  estimatedReadingTime = 10,
  onProgressUpdate,
  onChapterComplete
}: ChapterProgressTrackerProps) {
  const { data: session } = useSession()
  const { toast } = useToast()
  
  // Reading state
  const [isReading, setIsReading] = useState(false)
  const [readingProgress, setReadingProgress] = useState(0)
  const [timeSpent, setTimeSpent] = useState(0) // in seconds
  const [startTime, setStartTime] = useState<Date | null>(null)
  const [isCompleted, setIsCompleted] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)

  // Refs for tracking
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const progressRef = useRef(0)
  const timeRef = useRef(0)

  // Auto-save interval (every 30 seconds)
  const AUTO_SAVE_INTERVAL = 30000

  // Load existing progress
  useEffect(() => {
    if (session?.user && novelId) {
      loadProgress()
    }
  }, [session, novelId, chapterId])

  // Auto-save progress
  useEffect(() => {
    if (isReading && session?.user) {
      const saveInterval = setInterval(() => {
        saveProgress(false) // Auto-save without toast
      }, AUTO_SAVE_INTERVAL)

      return () => clearInterval(saveInterval)
    }
  }, [isReading, session])

  // Update reading time
  useEffect(() => {
    if (isReading) {
      intervalRef.current = setInterval(() => {
        setTimeSpent(prev => {
          const newTime = prev + 1
          timeRef.current = newTime
          return newTime
        })
      }, 1000)
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [isReading])

  const loadProgress = async () => {
    try {
      const response = await fetch(`/api/reading-progress/${novelId}`)
      if (response.ok) {
        const data = await response.json()
        const progress = data.readingProgress
        
        if (progress && progress.lastChapter?.id === chapterId) {
          setReadingProgress(progress.progress || 0)
          setTimeSpent(progress.totalTimeRead || 0)
          setIsCompleted(progress.progress >= 100)
          progressRef.current = progress.progress || 0
          timeRef.current = progress.totalTimeRead || 0
        }
      }
    } catch (error) {
      console.error("Failed to load reading progress:", error)
    }
  }

  const saveProgress = async (showToast = true) => {
    if (!session?.user) return

    try {
      const response = await fetch("/api/reading-progress", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          novelId,
          chapterId,
          progress: progressRef.current,
          timeSpent: timeRef.current - (timeSpent - timeRef.current), // Only increment
        }),
      })

      if (response.ok) {
        setLastSaved(new Date())
        if (showToast) {
          toast({
            title: "Progress Saved",
            description: `Chapter progress: ${Math.round(progressRef.current)}%`,
          })
        }
        onProgressUpdate?.(progressRef.current)
      }
    } catch (error) {
      console.error("Failed to save progress:", error)
      if (showToast) {
        toast({
          title: "Save Failed",
          description: "Could not save reading progress",
          variant: "destructive",
        })
      }
    }
  }

  const startReading = () => {
    setIsReading(true)
    setStartTime(new Date())
  }

  const pauseReading = () => {
    setIsReading(false)
    saveProgress()
  }

  const markAsComplete = async () => {
    progressRef.current = 100
    setReadingProgress(100)
    setIsCompleted(true)
    setIsReading(false)
    
    await saveProgress()
    
    toast({
      title: "Chapter Completed! 🎉",
      description: `You finished "${chapterTitle}"`,
    })
    
    onChapterComplete?.()
  }

  const resetProgress = () => {
    progressRef.current = 0
    setReadingProgress(0)
    setTimeSpent(0)
    setIsCompleted(false)
    setIsReading(false)
    timeRef.current = 0
    saveProgress()
  }

  const updateProgress = (newProgress: number) => {
    progressRef.current = Math.min(100, Math.max(0, newProgress))
    setReadingProgress(progressRef.current)
    
    if (progressRef.current >= 100 && !isCompleted) {
      markAsComplete()
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getReadingSpeed = () => {
    if (timeSpent === 0) return 0
    return Math.round((readingProgress / 100) * estimatedReadingTime * 60 / timeSpent * 100) / 100
  }

  return (
    <Card className="w-full">
      <CardContent className="p-4 space-y-4">
        {/* Chapter Info */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h3 className="font-medium text-sm">
              Chapter {chapterOrder}: {chapterTitle}
            </h3>
            <p className="text-xs text-muted-foreground">
              {chapterOrder} of {totalChapters} chapters
            </p>
          </div>
          <div className="flex items-center gap-2">
            {isCompleted && (
              <Badge variant="secondary" className="text-xs">
                <CheckCircle className="h-3 w-3 mr-1" />
                Complete
              </Badge>
            )}
            <Badge variant="outline" className="text-xs">
              <Timer className="h-3 w-3 mr-1" />
              ~{estimatedReadingTime}m
            </Badge>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-xs">
            <span>Reading Progress</span>
            <span>{Math.round(readingProgress)}%</span>
          </div>
          <Progress value={readingProgress} className="h-2" />
        </div>

        {/* Reading Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {!isReading ? (
              <Button
                size="sm"
                onClick={startReading}
                disabled={isCompleted}
                className="h-8"
              >
                <Play className="h-3 w-3 mr-1" />
                {readingProgress === 0 ? "Start" : "Resume"}
              </Button>
            ) : (
              <Button
                size="sm"
                variant="outline"
                onClick={pauseReading}
                className="h-8"
              >
                <Pause className="h-3 w-3 mr-1" />
                Pause
              </Button>
            )}

            {!isCompleted && readingProgress > 0 && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => updateProgress(readingProgress + 25)}
                className="h-8"
              >
                +25%
              </Button>
            )}

            {!isCompleted && (
              <Button
                size="sm"
                variant="outline"
                onClick={markAsComplete}
                className="h-8"
              >
                <CheckCircle className="h-3 w-3 mr-1" />
                Complete
              </Button>
            )}
          </div>

          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            <span>{formatTime(timeSpent)}</span>
            {getReadingSpeed() > 0 && (
              <span className="text-xs">
                ({getReadingSpeed()}x speed)
              </span>
            )}
          </div>
        </div>

        {/* Reading Stats */}
        <div className="grid grid-cols-3 gap-2 text-xs">
          <div className="text-center p-2 bg-muted/50 rounded">
            <div className="font-medium">{Math.round(readingProgress)}%</div>
            <div className="text-muted-foreground">Progress</div>
          </div>
          <div className="text-center p-2 bg-muted/50 rounded">
            <div className="font-medium">{formatTime(timeSpent)}</div>
            <div className="text-muted-foreground">Time Read</div>
          </div>
          <div className="text-center p-2 bg-muted/50 rounded">
            <div className="font-medium">
              {estimatedReadingTime - Math.round(timeSpent / 60) > 0 
                ? `${estimatedReadingTime - Math.round(timeSpent / 60)}m` 
                : "0m"}
            </div>
            <div className="text-muted-foreground">Remaining</div>
          </div>
        </div>

        {/* Last Saved */}
        {lastSaved && (
          <div className="text-xs text-muted-foreground text-center">
            Last saved {formatDistanceToNow(lastSaved, { addSuffix: true })}
          </div>
        )}

        {/* Reset Option */}
        {readingProgress > 0 && (
          <div className="flex justify-center">
            <Button
              size="sm"
              variant="ghost"
              onClick={resetProgress}
              className="h-6 text-xs text-muted-foreground hover:text-destructive"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Reset Progress
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
