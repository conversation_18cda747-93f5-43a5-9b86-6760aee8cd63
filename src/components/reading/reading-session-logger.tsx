"use client"

import { useState, useEffect, useRef } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { 
  BookOpen, 
  Clock, 
  Eye, 
  Target, 
  TrendingUp,
  Calendar,
  Zap,
  Award
} from "lucide-react"
import { formatDistanceToNow, format } from "date-fns"

interface ReadingSession {
  id: string
  startTime: Date
  endTime?: Date
  duration: number // in seconds
  chaptersRead: string[]
  wordsRead: number
  averageReadingSpeed: number // words per minute
  breaks: number
  focusScore: number // 0-100 based on reading consistency
}

interface ReadingSessionLoggerProps {
  novelId: string
  chapterId: string
  chapterWordCount?: number
  onSessionEnd?: (session: ReadingSession) => void
}

export function ReadingSessionLogger({
  novelId,
  chapterId,
  chapterWordCount = 2000,
  onSessionEnd
}: ReadingSessionLoggerProps) {
  const { data: session } = useSession()
  const { toast } = useToast()

  // Session state
  const [currentSession, setCurrentSession] = useState<ReadingSession | null>(null)
  const [isActive, setIsActive] = useState(false)
  const [sessionHistory, setSessionHistory] = useState<ReadingSession[]>([])
  const [todayStats, setTodayStats] = useState({
    sessionsCount: 0,
    totalTime: 0,
    chaptersRead: 0,
    wordsRead: 0,
    averageSpeed: 0
  })

  // Tracking refs
  const startTimeRef = useRef<Date | null>(null)
  const lastActivityRef = useRef<Date>(new Date())
  const wordsReadRef = useRef(0)
  const breaksRef = useRef(0)
  const activityCheckInterval = useRef<NodeJS.Timeout | null>(null)

  // Load session history on mount
  useEffect(() => {
    loadSessionHistory()
    loadTodayStats()
  }, [session])

  // Activity monitoring
  useEffect(() => {
    if (isActive) {
      // Monitor user activity (scroll, mouse movement, etc.)
      const handleActivity = () => {
        lastActivityRef.current = new Date()
      }

      const checkActivity = () => {
        const now = new Date()
        const timeSinceActivity = now.getTime() - lastActivityRef.current.getTime()
        
        // If inactive for more than 2 minutes, count as a break
        if (timeSinceActivity > 120000) { // 2 minutes
          breaksRef.current += 1
          lastActivityRef.current = now
        }
      }

      // Add activity listeners
      window.addEventListener('scroll', handleActivity)
      window.addEventListener('mousemove', handleActivity)
      window.addEventListener('keydown', handleActivity)
      window.addEventListener('click', handleActivity)

      // Check activity every 30 seconds
      activityCheckInterval.current = setInterval(checkActivity, 30000)

      return () => {
        window.removeEventListener('scroll', handleActivity)
        window.removeEventListener('mousemove', handleActivity)
        window.removeEventListener('keydown', handleActivity)
        window.removeEventListener('click', handleActivity)
        
        if (activityCheckInterval.current) {
          clearInterval(activityCheckInterval.current)
        }
      }
    }
  }, [isActive])

  const loadSessionHistory = async () => {
    try {
      const response = await fetch(`/api/reading-sessions?novelId=${novelId}&limit=10`)
      if (response.ok) {
        const data = await response.json()
        setSessionHistory(data.sessions || [])
      }
    } catch (error) {
      console.error("Failed to load session history:", error)
    }
  }

  const loadTodayStats = async () => {
    try {
      const today = new Date().toISOString().split('T')[0]
      const response = await fetch(`/api/reading-sessions/stats?date=${today}`)
      if (response.ok) {
        const data = await response.json()
        setTodayStats(data.stats)
      }
    } catch (error) {
      console.error("Failed to load today's stats:", error)
    }
  }

  const startSession = () => {
    const now = new Date()
    const newSession: ReadingSession = {
      id: `session-${now.getTime()}`,
      startTime: now,
      duration: 0,
      chaptersRead: [chapterId],
      wordsRead: 0,
      averageReadingSpeed: 0,
      breaks: 0,
      focusScore: 100
    }

    setCurrentSession(newSession)
    setIsActive(true)
    startTimeRef.current = now
    lastActivityRef.current = now
    wordsReadRef.current = 0
    breaksRef.current = 0

    toast({
      title: "Reading Session Started",
      description: "Your reading progress is being tracked",
    })
  }

  const endSession = async () => {
    if (!currentSession || !startTimeRef.current) return

    const endTime = new Date()
    const duration = Math.floor((endTime.getTime() - startTimeRef.current.getTime()) / 1000)
    const wordsRead = wordsReadRef.current || Math.floor(chapterWordCount * 0.8) // Estimate if not tracked
    const averageSpeed = duration > 0 ? Math.round((wordsRead / duration) * 60) : 0
    const focusScore = Math.max(0, 100 - (breaksRef.current * 10)) // Reduce score for breaks

    const completedSession: ReadingSession = {
      ...currentSession,
      endTime,
      duration,
      wordsRead,
      averageReadingSpeed: averageSpeed,
      breaks: breaksRef.current,
      focusScore
    }

    // Save session to backend
    try {
      await fetch('/api/reading-sessions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          novelId,
          chapterId,
          session: completedSession
        })
      })

      setSessionHistory(prev => [completedSession, ...prev.slice(0, 9)])
      setCurrentSession(null)
      setIsActive(false)
      onSessionEnd?.(completedSession)

      // Update today's stats
      setTodayStats(prev => ({
        sessionsCount: prev.sessionsCount + 1,
        totalTime: prev.totalTime + duration,
        chaptersRead: prev.chaptersRead + 1,
        wordsRead: prev.wordsRead + wordsRead,
        averageSpeed: Math.round((prev.wordsRead + wordsRead) / (prev.totalTime + duration) * 60)
      }))

      toast({
        title: "Session Completed! 📚",
        description: `Read for ${Math.round(duration / 60)} minutes with ${focusScore}% focus`,
      })

    } catch (error) {
      console.error("Failed to save session:", error)
      toast({
        title: "Session Save Failed",
        description: "Could not save reading session",
        variant: "destructive"
      })
    }
  }

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60

    if (hours > 0) {
      return `${hours}h ${minutes}m`
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`
    } else {
      return `${secs}s`
    }
  }

  const getCurrentSessionDuration = () => {
    if (!startTimeRef.current) return 0
    return Math.floor((new Date().getTime() - startTimeRef.current.getTime()) / 1000)
  }

  const getFocusScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600"
    if (score >= 60) return "text-yellow-600"
    return "text-red-600"
  }

  return (
    <div className="space-y-4">
      {/* Current Session */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <BookOpen className="h-5 w-5" />
            Reading Session
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {!isActive ? (
            <div className="text-center space-y-3">
              <p className="text-muted-foreground">Start tracking your reading session</p>
              <Button onClick={startSession} className="w-full">
                <Zap className="h-4 w-4 mr-2" />
                Start Reading Session
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Session Active</p>
                  <p className="text-xs text-muted-foreground">
                    Started {formatDistanceToNow(startTimeRef.current!, { addSuffix: true })}
                  </p>
                </div>
                <Badge variant="secondary" className="animate-pulse">
                  <Clock className="h-3 w-3 mr-1" />
                  {formatDuration(getCurrentSessionDuration())}
                </Badge>
              </div>

              <div className="grid grid-cols-3 gap-2 text-xs">
                <div className="text-center p-2 bg-muted/50 rounded">
                  <div className="font-medium">{formatDuration(getCurrentSessionDuration())}</div>
                  <div className="text-muted-foreground">Duration</div>
                </div>
                <div className="text-center p-2 bg-muted/50 rounded">
                  <div className="font-medium">{breaksRef.current}</div>
                  <div className="text-muted-foreground">Breaks</div>
                </div>
                <div className="text-center p-2 bg-muted/50 rounded">
                  <div className={`font-medium ${getFocusScoreColor(Math.max(0, 100 - (breaksRef.current * 10)))}`}>
                    {Math.max(0, 100 - (breaksRef.current * 10))}%
                  </div>
                  <div className="text-muted-foreground">Focus</div>
                </div>
              </div>

              <Button onClick={endSession} variant="outline" className="w-full">
                End Session
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Today's Stats */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Calendar className="h-5 w-5" />
            Today's Reading
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Total Time</span>
              </div>
              <p className="text-2xl font-bold">{formatDuration(todayStats.totalTime)}</p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <BookOpen className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Chapters</span>
              </div>
              <p className="text-2xl font-bold">{todayStats.chaptersRead}</p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Reading Speed</span>
              </div>
              <p className="text-2xl font-bold">{todayStats.averageSpeed} <span className="text-sm font-normal">wpm</span></p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Award className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Sessions</span>
              </div>
              <p className="text-2xl font-bold">{todayStats.sessionsCount}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Sessions */}
      {sessionHistory.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Eye className="h-5 w-5" />
              Recent Sessions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {sessionHistory.slice(0, 3).map((session, index) => (
                <div key={session.id} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">
                      {format(new Date(session.startTime), 'MMM d, h:mm a')}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {formatDuration(session.duration)} • {session.averageReadingSpeed} wpm
                    </p>
                  </div>
                  <Badge 
                    variant="outline" 
                    className={`text-xs ${getFocusScoreColor(session.focusScore)}`}
                  >
                    {session.focusScore}% focus
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
