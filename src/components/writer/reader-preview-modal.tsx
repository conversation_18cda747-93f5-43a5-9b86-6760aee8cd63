"use client"

import { useState } from "react"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Eye, 
  Lock, 
  Coins, 
  Crown, 
  CreditCard, 
  ShoppingCart,
  AlertTriangle,
  CheckCircle,
  Smartphone,
  Monitor
} from "lucide-react"
import { formatCredits, formatCreditPrice } from "@/lib/credits"
import { SubscriptionTier } from "@prisma/client"

interface Chapter {
  id: string
  title: string
  order: number
  isPremium: boolean
  creditPrice?: number | null
  requiredTier?: SubscriptionTier | null
  content: string
}

interface ReaderPreviewModalProps {
  chapter: Chapter | null
  isOpen: boolean
  onClose: () => void
}

export function ReaderPreviewModal({ chapter, isOpen, onClose }: ReaderPreviewModalProps) {
  const [viewMode, setViewMode] = useState<'desktop' | 'mobile'>('desktop')
  const [readerType, setReaderType] = useState<'free' | 'premium' | 'premium_plus'>('free')

  if (!chapter) return null

  const mockUserBalance = 25 // Mock user credit balance
  const canAfford = chapter.creditPrice ? mockUserBalance >= chapter.creditPrice : true

  const getAccessInfo = () => {
    if (!chapter.isPremium) {
      return {
        hasAccess: true,
        accessMethod: 'free',
        message: 'This chapter is free for all readers'
      }
    }

    // Check subscription access
    if (readerType === 'premium' && chapter.requiredTier === 'PREMIUM') {
      return {
        hasAccess: true,
        accessMethod: 'subscription',
        message: 'Premium subscriber has free access'
      }
    }

    if (readerType === 'premium_plus') {
      return {
        hasAccess: true,
        accessMethod: 'subscription',
        message: 'Premium Plus subscriber has free access'
      }
    }

    // Credit purchase required
    return {
      hasAccess: false,
      accessMethod: 'credit',
      message: canAfford 
        ? `Reader can unlock with ${formatCredits(chapter.creditPrice || 0)}`
        : `Reader needs ${chapter.creditPrice! - mockUserBalance} more credits`
    }
  }

  const accessInfo = getAccessInfo()

  const PreviewContent = ({ isMobile = false }: { isMobile?: boolean }) => (
    <div className={`space-y-4 ${isMobile ? 'text-sm' : ''}`}>
      {/* Chapter Header */}
      <Card className={isMobile ? 'p-3' : ''}>
        <CardHeader className={isMobile ? 'pb-2' : ''}>
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <CardTitle className={isMobile ? 'text-lg' : 'text-xl'}>
                Chapter {chapter.order}: {chapter.title}
              </CardTitle>
              <div className="flex items-center gap-2">
                {chapter.isPremium ? (
                  <Badge variant="outline" className="text-blue-600">
                    <Crown className="h-3 w-3 mr-1" />
                    Premium
                  </Badge>
                ) : (
                  <Badge variant="outline" className="text-green-600">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Free
                  </Badge>
                )}
                {chapter.creditPrice && (
                  <Badge variant="secondary">
                    {formatCredits(chapter.creditPrice)}
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Content Area */}
      <Card>
        <CardContent className={`pt-6 ${isMobile ? 'px-3' : ''}`}>
          {accessInfo.hasAccess ? (
            // Full content for users with access
            <div className="space-y-4">
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  {accessInfo.message}
                </AlertDescription>
              </Alert>
              <div className="prose prose-sm max-w-none">
                <p>{chapter.content.substring(0, 500)}...</p>
                <p className="text-muted-foreground italic">
                  [Full chapter content would be displayed here]
                </p>
              </div>
            </div>
          ) : (
            // Paywall for users without access
            <div className="space-y-4">
              {/* Preview Content */}
              <div className="prose prose-sm max-w-none">
                <p>{chapter.content.substring(0, 200)}...</p>
              </div>

              {/* Paywall */}
              <div className="border-2 border-dashed border-gray-300 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-6 text-center space-y-4">
                <div className="mx-auto p-3 bg-gray-200 rounded-full w-fit">
                  <Lock className="h-6 w-6 text-gray-600" />
                </div>
                
                <div>
                  <h3 className="font-semibold text-lg mb-2">Premium Content</h3>
                  <p className="text-muted-foreground">
                    Unlock this chapter to continue reading
                  </p>
                </div>

                {/* Pricing Options */}
                <div className="space-y-3">
                  {/* Credit Option */}
                  {chapter.creditPrice && (
                    <Card className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Coins className="h-5 w-5 text-yellow-500" />
                          <div className="text-left">
                            <div className="font-medium">Unlock with Credits</div>
                            <div className="text-sm text-muted-foreground">
                              {formatCreditPrice(chapter.creditPrice)}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-muted-foreground">
                            Your balance: {formatCredits(mockUserBalance)}
                          </div>
                          {canAfford ? (
                            <Button size="sm" className="mt-1">
                              <ShoppingCart className="h-4 w-4 mr-1" />
                              Unlock
                            </Button>
                          ) : (
                            <div className="space-y-1">
                              <Alert className="p-2">
                                <AlertTriangle className="h-3 w-3" />
                                <AlertDescription className="text-xs">
                                  Need {chapter.creditPrice - mockUserBalance} more credits
                                </AlertDescription>
                              </Alert>
                              <Button size="sm" variant="outline">
                                <Coins className="h-4 w-4 mr-1" />
                                Buy Credits
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>
                    </Card>
                  )}

                  {/* Subscription Option */}
                  {chapter.requiredTier && (
                    <Card className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Crown className="h-5 w-5 text-purple-500" />
                          <div className="text-left">
                            <div className="font-medium">
                              {chapter.requiredTier === 'PREMIUM' ? 'Premium' : 'Premium Plus'} Subscription
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {chapter.requiredTier === 'PREMIUM' ? '$9.99/month' : '$19.99/month'}
                            </div>
                          </div>
                        </div>
                        <Button size="sm" variant="outline">
                          <CreditCard className="h-4 w-4 mr-1" />
                          Subscribe
                        </Button>
                      </div>
                    </Card>
                  )}
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Reader Preview: Chapter {chapter.order}
          </DialogTitle>
          <DialogDescription>
            See how readers will experience this chapter based on their subscription status
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Preview Controls */}
          <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
            <div className="space-y-2">
              <div className="text-sm font-medium">Reader Type:</div>
              <Tabs value={readerType} onValueChange={(value: any) => setReaderType(value)}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="free">Free User</TabsTrigger>
                  <TabsTrigger value="premium">Premium</TabsTrigger>
                  <TabsTrigger value="premium_plus">Premium Plus</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            <div className="space-y-2">
              <div className="text-sm font-medium">Device:</div>
              <Tabs value={viewMode} onValueChange={(value: any) => setViewMode(value)}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="desktop" className="flex items-center gap-1">
                    <Monitor className="h-3 w-3" />
                    Desktop
                  </TabsTrigger>
                  <TabsTrigger value="mobile" className="flex items-center gap-1">
                    <Smartphone className="h-3 w-3" />
                    Mobile
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </div>

          {/* Preview Area */}
          <div className={`border rounded-lg ${viewMode === 'mobile' ? 'max-w-sm mx-auto' : ''}`}>
            <div className="p-4">
              <PreviewContent isMobile={viewMode === 'mobile'} />
            </div>
          </div>

          {/* Access Summary */}
          <Alert>
            <Eye className="h-4 w-4" />
            <AlertDescription>
              <strong>Preview Summary:</strong> {accessInfo.message}
              {!accessInfo.hasAccess && chapter.creditPrice && (
                <span className="ml-2">
                  • Reader balance: {formatCredits(mockUserBalance)}
                  • Required: {formatCredits(chapter.creditPrice)}
                </span>
              )}
            </AlertDescription>
          </Alert>
        </div>
      </DialogContent>
    </Dialog>
  )
}
