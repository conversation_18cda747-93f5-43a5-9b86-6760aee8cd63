"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { 
  Coins, 
  Crown, 
  CheckCircle, 
  Edit,
  Save,
  X
} from "lucide-react"
import { formatCredits } from "@/lib/credits"

interface Chapter {
  id: string
  title: string
  order: number
  isPremium: boolean
  creditPrice?: number | null
}

interface QuickCreditSetterProps {
  chapter: Chapter
  onUpdate?: () => void
}

export function QuickCreditSetter({ chapter, onUpdate }: QuickCreditSetterProps) {
  const { toast } = useToast()
  const [isEditing, setIsEditing] = useState(false)
  const [creditPrice, setCreditPrice] = useState(chapter.creditPrice || 5)
  const [isLoading, setIsLoading] = useState(false)

  const handleSave = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/chapters/${chapter.id}/pricing`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isPremium: true,
          creditPrice: creditPrice,
          requiredTier: 'PREMIUM',
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update pricing')
      }

      toast({
        title: "Credits Set!",
        description: `Chapter now costs ${formatCredits(creditPrice)}`,
      })

      setIsEditing(false)
      onUpdate?.()
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to set credits",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleMakeFree = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/chapters/${chapter.id}/pricing`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isPremium: false,
          creditPrice: null,
          requiredTier: null,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update pricing')
      }

      toast({
        title: "Chapter Made Free!",
        description: "Chapter is now free for all readers",
      })

      onUpdate?.()
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to make free",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const quickPrices = [3, 5, 8, 10, 15]

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center justify-between">
          <span>Chapter {chapter.order}: {chapter.title}</span>
          <div className="flex items-center gap-2">
            {chapter.isPremium ? (
              <Badge variant="outline" className="text-blue-600">
                <Crown className="h-3 w-3 mr-1" />
                {formatCredits(chapter.creditPrice || 0)}
              </Badge>
            ) : (
              <Badge variant="outline" className="text-green-600">
                <CheckCircle className="h-3 w-3 mr-1" />
                Free
              </Badge>
            )}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {!isEditing ? (
          // Display Mode
          <div className="space-y-3">
            <div className="text-sm text-muted-foreground">
              Current Status: {chapter.isPremium ? `Premium - ${formatCredits(chapter.creditPrice || 0)}` : 'Free'}
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(true)}
                className="flex items-center gap-1"
              >
                <Edit className="h-3 w-3" />
                Set Credits
              </Button>
              
              {chapter.isPremium && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleMakeFree}
                  disabled={isLoading}
                  className="flex items-center gap-1"
                >
                  <CheckCircle className="h-3 w-3" />
                  Make Free
                </Button>
              )}
            </div>
          </div>
        ) : (
          // Edit Mode
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="creditPrice">Credit Price</Label>
              <Input
                id="creditPrice"
                type="number"
                min="1"
                max="1000"
                value={creditPrice}
                onChange={(e) => setCreditPrice(parseInt(e.target.value) || 1)}
                placeholder="Enter credit amount"
              />
              <div className="text-xs text-muted-foreground">
                Equivalent to ${(creditPrice * 0.10).toFixed(2)} USD
              </div>
            </div>

            {/* Quick Price Buttons */}
            <div className="space-y-2">
              <Label>Quick Set:</Label>
              <div className="flex items-center gap-2 flex-wrap">
                {quickPrices.map((price) => (
                  <Button
                    key={price}
                    variant="outline"
                    size="sm"
                    onClick={() => setCreditPrice(price)}
                    className={`flex items-center gap-1 ${creditPrice === price ? 'bg-primary text-primary-foreground' : ''}`}
                  >
                    <Coins className="h-3 w-3" />
                    {price}
                  </Button>
                ))}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              <Button
                onClick={handleSave}
                disabled={isLoading || creditPrice < 1}
                size="sm"
                className="flex items-center gap-1"
              >
                <Save className="h-3 w-3" />
                {isLoading ? "Saving..." : "Save Credits"}
              </Button>
              
              <Button
                variant="outline"
                onClick={() => {
                  setIsEditing(false)
                  setCreditPrice(chapter.creditPrice || 5)
                }}
                size="sm"
                className="flex items-center gap-1"
              >
                <X className="h-3 w-3" />
                Cancel
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
