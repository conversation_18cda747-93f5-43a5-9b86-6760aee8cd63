"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { Coins, DollarSign, Users, TrendingUp, Info } from "lucide-react"
import { formatCredits, formatCreditPrice, creditsToUSD, CREDIT_CONFIG } from "@/lib/credits"
import { SubscriptionTier } from "@prisma/client"

const pricingSchema = z.object({
  isPremium: z.boolean(),
  creditPrice: z.number().int().min(1).max(1000).optional(),
  requiredTier: z.enum(['FREE', 'PREMIUM', 'PREMIUM_PLUS']).optional(),
})

type PricingFormData = z.infer<typeof pricingSchema>

interface ChapterPricingFormProps {
  chapterId: string
  initialData?: {
    isPremium: boolean
    creditPrice?: number | null
    requiredTier?: SubscriptionTier | null
  }
  analytics?: {
    purchaseCount: number
    totalCreditRevenue: number
  }
  onUpdate?: () => void
}

export function ChapterPricingForm({ 
  chapterId, 
  initialData, 
  analytics,
  onUpdate 
}: ChapterPricingFormProps) {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<PricingFormData>({
    resolver: zodResolver(pricingSchema),
    defaultValues: {
      isPremium: initialData?.isPremium || false,
      creditPrice: initialData?.creditPrice || CREDIT_CONFIG.DEFAULT_CHAPTER_CREDITS,
      requiredTier: initialData?.requiredTier || 'PREMIUM',
    }
  })

  const isPremium = form.watch('isPremium')
  const creditPrice = form.watch('creditPrice')

  const onSubmit = async (data: PricingFormData) => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/chapters/${chapterId}/pricing`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update pricing')
      }

      toast({
        title: "Pricing Updated",
        description: "Chapter pricing has been updated successfully.",
      })

      onUpdate?.()
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update pricing",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const suggestedPrices = [
    { credits: 3, label: "Budget-friendly", description: "Great for new writers" },
    { credits: 5, label: "Standard", description: "Most popular choice" },
    { credits: 8, label: "Premium", description: "For high-quality content" },
    { credits: 12, label: "Exclusive", description: "For special chapters" },
  ]

  return (
    <div className="space-y-6">
      {/* Analytics Card */}
      {analytics && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Chapter Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{analytics.purchaseCount}</div>
                <div className="text-sm text-muted-foreground">Credit Purchases</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {formatCredits(analytics.totalCreditRevenue)}
                </div>
                <div className="text-sm text-muted-foreground">
                  Total Revenue (${creditsToUSD(analytics.totalCreditRevenue).toFixed(2)})
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Pricing Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Coins className="h-5 w-5 text-yellow-500" />
            Chapter Pricing
          </CardTitle>
          <CardDescription>
            Set pricing for this chapter. Readers can unlock with credits or subscription.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Premium Toggle */}
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="isPremium">Premium Content</Label>
                <div className="text-sm text-muted-foreground">
                  Require payment or subscription to access
                </div>
              </div>
              <Switch
                id="isPremium"
                checked={isPremium}
                onCheckedChange={(checked) => form.setValue('isPremium', checked)}
              />
            </div>

            {isPremium && (
              <>
                {/* Credit Price */}
                <div className="space-y-2">
                  <Label htmlFor="creditPrice">Credit Price</Label>
                  <Input
                    id="creditPrice"
                    type="number"
                    min="1"
                    max="1000"
                    {...form.register('creditPrice', { valueAsNumber: true })}
                  />
                  {creditPrice && (
                    <div className="text-sm text-muted-foreground">
                      Equivalent to ${creditsToUSD(creditPrice).toFixed(2)} USD
                    </div>
                  )}
                </div>

                {/* Suggested Prices */}
                <div className="space-y-2">
                  <Label>Suggested Prices</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {suggestedPrices.map((suggestion) => (
                      <Button
                        key={suggestion.credits}
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => form.setValue('creditPrice', suggestion.credits)}
                        className="justify-start"
                      >
                        <div className="text-left">
                          <div className="font-medium">{formatCredits(suggestion.credits)}</div>
                          <div className="text-xs text-muted-foreground">{suggestion.label}</div>
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Required Tier */}
                <div className="space-y-2">
                  <Label htmlFor="requiredTier">Subscription Tier Access</Label>
                  <Select
                    value={form.watch('requiredTier')}
                    onValueChange={(value) => form.setValue('requiredTier', value as SubscriptionTier)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="PREMIUM">Premium ($9.99/month)</SelectItem>
                      <SelectItem value="PREMIUM_PLUS">Premium Plus ($19.99/month)</SelectItem>
                    </SelectContent>
                  </Select>
                  <div className="text-sm text-muted-foreground">
                    Subscribers of this tier or higher get free access
                  </div>
                </div>

                {/* Revenue Split Info */}
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    You earn {CREDIT_CONFIG.AUTHOR_PERCENTAGE}% of credit purchases. 
                    Platform fee: {CREDIT_CONFIG.PLATFORM_PERCENTAGE}%
                  </AlertDescription>
                </Alert>
              </>
            )}

            <Button type="submit" disabled={isLoading} className="w-full">
              {isLoading ? "Updating..." : "Update Pricing"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
