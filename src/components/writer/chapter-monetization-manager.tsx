"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { 
  Coins, 
  DollarSign, 
  Users, 
  TrendingUp, 
  Info, 
  Eye,
  Settings,
  Zap,
  Crown,
  CheckCircle,
  AlertTriangle,
  Lightbulb
} from "lucide-react"
import { formatCredits, formatCreditPrice, creditsToUSD, CREDIT_CONFIG } from "@/lib/credits"
import { SubscriptionTier } from "@prisma/client"
import { ReaderPreviewModal } from "./reader-preview-modal"

interface Chapter {
  id: string
  title: string
  order: number
  status: string
  isPremium: boolean
  creditPrice?: number | null
  requiredTier?: SubscriptionTier | null
  content?: string
  analytics?: {
    purchaseCount: number
    totalCreditRevenue: number
    views: number
  }
}

interface ChapterMonetizationManagerProps {
  novelId: string
  chapters: Chapter[]
  onUpdate?: () => void
}

const bulkPricingSchema = z.object({
  selectedChapters: z.array(z.string()).min(1, "Select at least one chapter"),
  action: z.enum(['set_premium', 'set_free', 'set_price', 'set_tier']),
  creditPrice: z.number().int().min(1).max(1000).optional(),
  requiredTier: z.enum(['PREMIUM', 'PREMIUM_PLUS']).optional(),
})

type BulkPricingData = z.infer<typeof bulkPricingSchema>

export function ChapterMonetizationManager({ 
  novelId, 
  chapters, 
  onUpdate 
}: ChapterMonetizationManagerProps) {
  const { toast } = useToast()
  const [selectedChapters, setSelectedChapters] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showBulkEdit, setShowBulkEdit] = useState(false)
  const [previewChapter, setPreviewChapter] = useState<Chapter | null>(null)
  const [showPreviewModal, setShowPreviewModal] = useState(false)

  const form = useForm<BulkPricingData>({
    resolver: zodResolver(bulkPricingSchema),
    defaultValues: {
      selectedChapters: [],
      action: 'set_price',
      creditPrice: CREDIT_CONFIG.DEFAULT_CHAPTER_CREDITS,
      requiredTier: 'PREMIUM',
    }
  })

  // Update form when selected chapters change
  useEffect(() => {
    form.setValue('selectedChapters', selectedChapters)
  }, [selectedChapters, form])

  const handleChapterSelect = (chapterId: string, checked: boolean) => {
    if (checked) {
      setSelectedChapters(prev => [...prev, chapterId])
    } else {
      setSelectedChapters(prev => prev.filter(id => id !== chapterId))
    }
  }

  const handleSelectAll = () => {
    if (selectedChapters.length === chapters.length) {
      setSelectedChapters([])
    } else {
      setSelectedChapters(chapters.map(c => c.id))
    }
  }

  const handleBulkUpdate = async (data: BulkPricingData) => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/novels/${novelId}/chapters/bulk-pricing`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update chapters')
      }

      const result = await response.json()
      
      toast({
        title: "Bulk Update Successful",
        description: `Updated ${result.updatedCount} chapters`,
      })

      setSelectedChapters([])
      setShowBulkEdit(false)
      onUpdate?.()
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update chapters",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleIndividualUpdate = async (chapterId: string, updates: Partial<Chapter>) => {
    try {
      const response = await fetch(`/api/chapters/${chapterId}/pricing`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update chapter')
      }

      toast({
        title: "Chapter Updated",
        description: "Pricing settings have been updated",
      })

      onUpdate?.()
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update chapter",
        variant: "destructive",
      })
    }
  }

  const getPricingRecommendation = (chapter: Chapter) => {
    const { order, analytics } = chapter
    
    if (order <= 3) {
      return {
        type: 'free',
        reason: 'Consider keeping early chapters free to attract readers',
        suggestedPrice: 0,
        icon: <Lightbulb className="h-4 w-4 text-yellow-500" />
      }
    }
    
    if (analytics && analytics.views > 100) {
      return {
        type: 'premium',
        reason: 'High engagement - good candidate for premium pricing',
        suggestedPrice: 8,
        icon: <TrendingUp className="h-4 w-4 text-green-500" />
      }
    }
    
    return {
      type: 'standard',
      reason: 'Standard pricing recommended',
      suggestedPrice: CREDIT_CONFIG.DEFAULT_CHAPTER_CREDITS,
      icon: <Coins className="h-4 w-4 text-blue-500" />
    }
  }

  const getChapterStats = () => {
    const total = chapters.length
    const premium = chapters.filter(c => c.isPremium).length
    const free = total - premium
    const totalRevenue = chapters.reduce((sum, c) => 
      sum + (c.analytics?.totalCreditRevenue || 0), 0
    )
    
    return { total, premium, free, totalRevenue }
  }

  const stats = getChapterStats()

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Chapter Monetization Overview
          </CardTitle>
          <CardDescription>
            Manage pricing and access settings for your chapters
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-muted/50 rounded">
              <div className="text-2xl font-bold">{stats.total}</div>
              <div className="text-sm text-muted-foreground">Total Chapters</div>
            </div>
            <div className="text-center p-3 bg-muted/50 rounded">
              <div className="text-2xl font-bold text-green-600">{stats.free}</div>
              <div className="text-sm text-muted-foreground">Free Chapters</div>
            </div>
            <div className="text-center p-3 bg-muted/50 rounded">
              <div className="text-2xl font-bold text-blue-600">{stats.premium}</div>
              <div className="text-sm text-muted-foreground">Premium Chapters</div>
            </div>
            <div className="text-center p-3 bg-muted/50 rounded">
              <div className="text-2xl font-bold text-purple-600">
                {formatCredits(stats.totalRevenue)}
              </div>
              <div className="text-sm text-muted-foreground">Total Revenue</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Bulk Actions
              </CardTitle>
              <CardDescription>
                Update multiple chapters at once
              </CardDescription>
            </div>
            <Button
              variant="outline"
              onClick={() => setShowBulkEdit(!showBulkEdit)}
              disabled={selectedChapters.length === 0}
            >
              {showBulkEdit ? 'Cancel' : 'Bulk Edit'}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-4">
            <Checkbox
              checked={selectedChapters.length === chapters.length}
              onCheckedChange={handleSelectAll}
            />
            <span className="text-sm">
              Select All ({selectedChapters.length} of {chapters.length} selected)
            </span>
          </div>

          {showBulkEdit && selectedChapters.length > 0 && (
            <form onSubmit={form.handleSubmit(handleBulkUpdate)} className="space-y-4 p-4 border rounded-lg bg-muted/20">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>Action</Label>
                  <Select
                    value={form.watch('action')}
                    onValueChange={(value: any) => form.setValue('action', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="set_free">Make Free</SelectItem>
                      <SelectItem value="set_premium">Make Premium</SelectItem>
                      <SelectItem value="set_price">Set Credit Price</SelectItem>
                      <SelectItem value="set_tier">Set Subscription Tier</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {form.watch('action') === 'set_price' && (
                  <div className="space-y-2">
                    <Label>Credit Price</Label>
                    <Input
                      type="number"
                      min="1"
                      max="1000"
                      {...form.register('creditPrice', { valueAsNumber: true })}
                    />
                  </div>
                )}

                {form.watch('action') === 'set_tier' && (
                  <div className="space-y-2">
                    <Label>Required Tier</Label>
                    <Select
                      value={form.watch('requiredTier')}
                      onValueChange={(value: any) => form.setValue('requiredTier', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="PREMIUM">Premium ($9.99/month)</SelectItem>
                        <SelectItem value="PREMIUM_PLUS">Premium Plus ($19.99/month)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>

              <Button type="submit" disabled={isLoading} className="w-full">
                {isLoading ? "Updating..." : `Update ${selectedChapters.length} Chapters`}
              </Button>
            </form>
          )}
        </CardContent>
      </Card>

      {/* Chapter List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Chapter Pricing Settings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {chapters.map((chapter) => {
              const recommendation = getPricingRecommendation(chapter)
              
              return (
                <div key={chapter.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Checkbox
                        checked={selectedChapters.includes(chapter.id)}
                        onCheckedChange={(checked) => 
                          handleChapterSelect(chapter.id, checked as boolean)
                        }
                      />
                      <div>
                        <h4 className="font-medium">
                          Chapter {chapter.order}: {chapter.title}
                        </h4>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant={chapter.status === "PUBLISHED" ? "default" : "secondary"}>
                            {chapter.status?.toLowerCase()}
                          </Badge>
                          {chapter.isPremium ? (
                            <Badge variant="outline" className="text-blue-600">
                              <Crown className="h-3 w-3 mr-1" />
                              Premium - {formatCredits(chapter.creditPrice || 0)}
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="text-green-600">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Free
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      {chapter.analytics && (
                        <div className="text-right text-sm text-muted-foreground">
                          <div>{chapter.analytics.views} views</div>
                          <div>{formatCredits(chapter.analytics.totalCreditRevenue)} earned</div>
                        </div>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setPreviewChapter(chapter)
                          setShowPreviewModal(true)
                        }}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        Preview
                      </Button>
                    </div>
                  </div>

                  {/* Pricing Recommendation */}
                  <Alert>
                    <div className="flex items-center gap-2">
                      {recommendation.icon}
                      <AlertDescription className="flex-1">
                        <strong>Recommendation:</strong> {recommendation.reason}
                        {recommendation.suggestedPrice > 0 && (
                          <span className="ml-2">
                            Suggested: {formatCredits(recommendation.suggestedPrice)}
                          </span>
                        )}
                      </AlertDescription>
                    </div>
                  </Alert>

                  {/* Quick Actions */}
                  <div className="flex items-center gap-2 pt-2 border-t">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleIndividualUpdate(chapter.id, { isPremium: false })}
                      disabled={!chapter.isPremium}
                    >
                      Make Free
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleIndividualUpdate(chapter.id, { 
                        isPremium: true, 
                        creditPrice: recommendation.suggestedPrice 
                      })}
                      disabled={chapter.isPremium}
                    >
                      Make Premium ({formatCredits(recommendation.suggestedPrice)})
                    </Button>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Reader Preview Modal */}
      <ReaderPreviewModal
        chapter={previewChapter}
        isOpen={showPreviewModal}
        onClose={() => {
          setShowPreviewModal(false)
          setPreviewChapter(null)
        }}
      />
    </div>
  )
}
