import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { isContentAuthor } from "@/lib/content-access"
import { isValidCreditPrice } from "@/lib/credits"
import { z } from "zod"

const bulkPricingSchema = z.object({
  selectedChapters: z.array(z.string()).min(1, "Select at least one chapter"),
  action: z.enum(['set_premium', 'set_free', 'set_price', 'set_tier']),
  creditPrice: z.number().int().min(1).max(1000).optional(),
  requiredTier: z.enum(['PREMIUM', 'PREMIUM_PLUS']).optional(),
})

// PUT /api/novels/[id]/chapters/bulk-pricing - Bulk update chapter pricing
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const novelId = params.id
    const body = await request.json()
    const { selectedChapters, action, creditPrice, requiredTier } = bulkPricingSchema.parse(body)

    // Verify novel exists and user is the author
    const novel = await prisma.novel.findUnique({
      where: { id: novelId },
      select: { authorId: true }
    })

    if (!novel) {
      return NextResponse.json({ error: "Novel not found" }, { status: 404 })
    }

    if (!isContentAuthor(session.user, novel.authorId)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Verify all selected chapters belong to this novel
    const chapters = await prisma.chapter.findMany({
      where: {
        id: { in: selectedChapters },
        novelId: novelId
      },
      select: { id: true, title: true, order: true }
    })

    if (chapters.length !== selectedChapters.length) {
      return NextResponse.json(
        { error: "Some chapters not found or don't belong to this novel" },
        { status: 400 }
      )
    }

    // Prepare update data based on action
    let updateData: any = {}

    switch (action) {
      case 'set_free':
        updateData = {
          isPremium: false,
          creditPrice: null,
          requiredTier: null,
        }
        break

      case 'set_premium':
        updateData = {
          isPremium: true,
          creditPrice: creditPrice || 5, // Default to 5 credits
          requiredTier: requiredTier || 'PREMIUM',
        }
        break

      case 'set_price':
        if (!creditPrice || !isValidCreditPrice(creditPrice)) {
          return NextResponse.json(
            { error: "Valid credit price is required" },
            { status: 400 }
          )
        }
        updateData = {
          isPremium: true,
          creditPrice: creditPrice,
          requiredTier: requiredTier || 'PREMIUM',
        }
        break

      case 'set_tier':
        if (!requiredTier) {
          return NextResponse.json(
            { error: "Required tier is required" },
            { status: 400 }
          )
        }
        updateData = {
          isPremium: true,
          requiredTier: requiredTier,
          // Keep existing credit price if set, otherwise use default
          creditPrice: creditPrice || 5,
        }
        break

      default:
        return NextResponse.json(
          { error: "Invalid action" },
          { status: 400 }
        )
    }

    // Perform bulk update
    const updateResult = await prisma.chapter.updateMany({
      where: {
        id: { in: selectedChapters },
        novelId: novelId
      },
      data: updateData
    })

    // Get updated chapters for response
    const updatedChapters = await prisma.chapter.findMany({
      where: {
        id: { in: selectedChapters },
        novelId: novelId
      },
      select: {
        id: true,
        title: true,
        order: true,
        isPremium: true,
        creditPrice: true,
        requiredTier: true,
      },
      orderBy: { order: 'asc' }
    })

    // Log the bulk update for analytics
    await prisma.$executeRaw`
      INSERT INTO chapter_pricing_logs (user_id, novel_id, action, chapter_count, created_at)
      VALUES (${session.user.id}, ${novelId}, ${action}, ${updateResult.count}, NOW())
      ON CONFLICT DO NOTHING
    `.catch(() => {
      // Ignore if table doesn't exist - this is optional logging
    })

    return NextResponse.json({
      success: true,
      updatedCount: updateResult.count,
      action: action,
      chapters: updatedChapters,
      message: `Successfully updated ${updateResult.count} chapters`,
    })

  } catch (error) {
    console.error("Error updating chapter pricing:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// GET /api/novels/[id]/chapters/bulk-pricing - Get pricing overview for novel
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const novelId = params.id

    // Verify novel exists and user is the author
    const novel = await prisma.novel.findUnique({
      where: { id: novelId },
      select: { 
        id: true,
        title: true,
        authorId: true 
      }
    })

    if (!novel) {
      return NextResponse.json({ error: "Novel not found" }, { status: 404 })
    }

    if (!isContentAuthor(session.user, novel.authorId)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Get chapters with pricing info and analytics
    const chapters = await prisma.chapter.findMany({
      where: { novelId },
      select: {
        id: true,
        title: true,
        order: true,
        status: true,
        isPremium: true,
        creditPrice: true,
        requiredTier: true,
        createdAt: true,
        content: true, // Add content for preview
      },
      orderBy: { order: 'asc' }
    })

    // Get analytics for each chapter (only if chapters exist)
    const chapterAnalytics = chapters.length > 0 ? await Promise.all(
      chapters.map(async (chapter) => {
        // Get purchase count
        const purchaseCount = await prisma.contentPurchase.count({
          where: {
            contentType: 'CHAPTER',
            contentId: chapter.id,
          }
        })

        // Get total revenue
        const revenueResult = await prisma.contentPurchase.aggregate({
          where: {
            contentType: 'CHAPTER',
            contentId: chapter.id,
          },
          _sum: {
            creditsSpent: true,
          }
        })

        // Estimate views (you might have a separate views table)
        const views = purchaseCount * 10 // Rough estimate: 10 views per purchase

        return {
          chapterId: chapter.id,
          purchaseCount,
          totalCreditRevenue: revenueResult._sum.creditsSpent || 0,
          views,
        }
      })
    ) : []

    // Combine chapters with analytics
    const chaptersWithAnalytics = chapters.map(chapter => {
      const analytics = chapterAnalytics.find(a => a.chapterId === chapter.id)
      return {
        ...chapter,
        analytics: analytics ? {
          purchaseCount: analytics.purchaseCount,
          totalCreditRevenue: analytics.totalCreditRevenue,
          views: analytics.views,
        } : undefined
      }
    })

    // Calculate overview stats
    const stats = {
      totalChapters: chapters.length,
      premiumChapters: chapters.filter(c => c.isPremium).length,
      freeChapters: chapters.filter(c => !c.isPremium).length,
      totalRevenue: chapterAnalytics.reduce((sum, a) => sum + a.totalCreditRevenue, 0),
      totalPurchases: chapterAnalytics.reduce((sum, a) => sum + a.purchaseCount, 0),
      averagePrice: chapters.filter(c => c.isPremium && c.creditPrice).length > 0
        ? Math.round(
            chapters
              .filter(c => c.isPremium && c.creditPrice)
              .reduce((sum, c) => sum + (c.creditPrice || 0), 0) /
            chapters.filter(c => c.isPremium && c.creditPrice).length
          )
        : 0,
    }

    return NextResponse.json({
      novel: {
        id: novel.id,
        title: novel.title,
      },
      chapters: chaptersWithAnalytics,
      stats,
      recommendations: generatePricingRecommendations(chaptersWithAnalytics),
    })

  } catch (error) {
    console.error("Error fetching chapter pricing overview:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// Helper function to generate pricing recommendations
function generatePricingRecommendations(chapters: any[]) {
  const recommendations = []

  // Check for early chapters that should be free
  const earlyPremiumChapters = chapters
    .filter(c => c.order <= 3 && c.isPremium)
    .length

  if (earlyPremiumChapters > 0) {
    recommendations.push({
      type: 'early_chapters_free',
      title: 'Consider Making Early Chapters Free',
      description: `You have ${earlyPremiumChapters} premium chapters in the first 3. Consider making them free to attract readers.`,
      priority: 'high',
      action: 'set_free',
      affectedChapters: chapters.filter(c => c.order <= 3 && c.isPremium).map(c => c.id),
    })
  }

  // Check for pricing consistency
  const premiumChapters = chapters.filter(c => c.isPremium && c.creditPrice)
  const prices = premiumChapters.map(c => c.creditPrice)
  const uniquePrices = [...new Set(prices)]

  if (uniquePrices.length > 3) {
    recommendations.push({
      type: 'price_consistency',
      title: 'Inconsistent Pricing',
      description: `You have ${uniquePrices.length} different prices. Consider standardizing for better user experience.`,
      priority: 'medium',
      suggestedPrice: Math.round(prices.reduce((sum, p) => sum + p, 0) / prices.length),
    })
  }

  return recommendations
}
