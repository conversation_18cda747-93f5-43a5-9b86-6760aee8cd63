import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { z } from "zod"
import { startOfDay, endOfDay, subDays, format } from "date-fns"

const statsQuerySchema = z.object({
  date: z.string().optional(), // YYYY-MM-DD format
  period: z.enum(['day', 'week', 'month', 'year']).optional().default('day'),
})

// GET /api/reading-sessions/stats - Get reading statistics
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const query = statsQuerySchema.parse({
      date: searchParams.get("date") || undefined,
      period: searchParams.get("period") as any || 'day',
    })

    const targetDate = query.date ? new Date(query.date) : new Date()
    
    let startDate: Date
    let endDate: Date

    // Calculate date range based on period
    switch (query.period) {
      case 'day':
        startDate = startOfDay(targetDate)
        endDate = endOfDay(targetDate)
        break
      case 'week':
        startDate = startOfDay(subDays(targetDate, 6))
        endDate = endOfDay(targetDate)
        break
      case 'month':
        startDate = startOfDay(subDays(targetDate, 29))
        endDate = endOfDay(targetDate)
        break
      case 'year':
        startDate = startOfDay(subDays(targetDate, 364))
        endDate = endOfDay(targetDate)
        break
      default:
        startDate = startOfDay(targetDate)
        endDate = endOfDay(targetDate)
    }

    // Get reading progress data for the period
    const readingData = await prisma.readingProgress.findMany({
      where: {
        userId: session.user.id,
        lastReadAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        novel: {
          select: {
            id: true,
            title: true,
          }
        },
        chapter: {
          select: {
            id: true,
            title: true,
            order: true,
          }
        }
      },
      orderBy: {
        lastReadAt: 'desc',
      }
    })

    // Calculate statistics
    const stats = {
      period: query.period,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      sessionsCount: readingData.length,
      totalTime: readingData.reduce((sum, item) => sum + item.totalTimeRead, 0),
      chaptersRead: readingData.filter(item => item.progress >= 100).length,
      wordsRead: readingData.reduce((sum, item) => sum + Math.floor(item.totalTimeRead * 3.5), 0),
      averageSpeed: 0,
      averageSessionDuration: 0,
      booksInProgress: new Set(readingData.map(item => item.novelId)).size,
      completionRate: 0,
      focusScore: 0,
      streakDays: 0,
    }

    // Calculate derived stats
    if (stats.totalTime > 0) {
      stats.averageSpeed = Math.round((stats.wordsRead / stats.totalTime) * 60) // words per minute
      stats.averageSessionDuration = Math.round(stats.totalTime / Math.max(1, stats.sessionsCount))
    }

    if (readingData.length > 0) {
      stats.completionRate = Math.round((stats.chaptersRead / readingData.length) * 100)
      // Simple focus score based on session consistency
      stats.focusScore = Math.min(100, Math.round((stats.totalTime / (readingData.length * 1800)) * 100)) // 30 min ideal session
    }

    // Calculate reading streak (consecutive days with reading activity)
    if (query.period === 'day') {
      stats.streakDays = await calculateReadingStreak(session.user.id, targetDate)
    }

    // Get daily breakdown for longer periods
    let dailyBreakdown: any[] = []
    if (query.period !== 'day') {
      dailyBreakdown = await getDailyBreakdown(session.user.id, startDate, endDate)
    }

    // Get top novels read in this period
    const novelStats = readingData.reduce((acc, item) => {
      const novelId = item.novelId
      if (!acc[novelId]) {
        acc[novelId] = {
          novel: item.novel,
          timeRead: 0,
          chaptersRead: 0,
          lastRead: item.lastReadAt,
        }
      }
      acc[novelId].timeRead += item.totalTimeRead
      if (item.progress >= 100) {
        acc[novelId].chaptersRead += 1
      }
      if (item.lastReadAt > acc[novelId].lastRead) {
        acc[novelId].lastRead = item.lastReadAt
      }
      return acc
    }, {} as Record<string, any>)

    const topNovels = Object.values(novelStats)
      .sort((a: any, b: any) => b.timeRead - a.timeRead)
      .slice(0, 5)

    return NextResponse.json({
      stats,
      dailyBreakdown,
      topNovels,
      recentActivity: readingData.slice(0, 10).map(item => ({
        id: item.id,
        novel: item.novel,
        chapter: item.chapter,
        timeRead: item.totalTimeRead,
        progress: item.progress,
        lastReadAt: item.lastReadAt,
      }))
    })

  } catch (error) {
    console.error("Error fetching reading stats:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// Helper function to calculate reading streak
async function calculateReadingStreak(userId: string, fromDate: Date): Promise<number> {
  let streak = 0
  let currentDate = startOfDay(fromDate)

  // Check each day going backwards
  for (let i = 0; i < 365; i++) { // Max 1 year streak
    const dayStart = startOfDay(currentDate)
    const dayEnd = endOfDay(currentDate)

    const hasActivity = await prisma.readingProgress.findFirst({
      where: {
        userId,
        lastReadAt: {
          gte: dayStart,
          lte: dayEnd,
        },
      },
    })

    if (hasActivity) {
      streak++
      currentDate = subDays(currentDate, 1)
    } else {
      break
    }
  }

  return streak
}

// Helper function to get daily breakdown
async function getDailyBreakdown(userId: string, startDate: Date, endDate: Date) {
  const days = []
  let currentDate = startOfDay(startDate)

  while (currentDate <= endDate) {
    const dayStart = startOfDay(currentDate)
    const dayEnd = endOfDay(currentDate)

    const dayData = await prisma.readingProgress.findMany({
      where: {
        userId,
        lastReadAt: {
          gte: dayStart,
          lte: dayEnd,
        },
      },
    })

    const dayStats = {
      date: format(currentDate, 'yyyy-MM-dd'),
      sessionsCount: dayData.length,
      totalTime: dayData.reduce((sum, item) => sum + item.totalTimeRead, 0),
      chaptersRead: dayData.filter(item => item.progress >= 100).length,
      wordsRead: dayData.reduce((sum, item) => sum + Math.floor(item.totalTimeRead * 3.5), 0),
    }

    days.push(dayStats)
    currentDate = new Date(currentDate.getTime() + 24 * 60 * 60 * 1000) // Add 1 day
  }

  return days
}
