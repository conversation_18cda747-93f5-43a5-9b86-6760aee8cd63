import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { ContentType, CreditTransactionType, CreditTransactionStatus, EarningType } from "@prisma/client"
import { calculateRevenueSplit } from "@/lib/stripe"
import { CreditNotificationService } from "@/lib/notifications/credit-notifications"
import { z } from "zod"

const spendCreditsSchema = z.object({
  contentType: z.enum(['NOVEL', 'CHAPTER']),
  contentId: z.string(),
  expectedPrice: z.number().int().min(1).optional(), // For price validation
})

// POST /api/credits/spend - Spend credits to unlock content
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { contentType, contentId } = spendCreditsSchema.parse(body)

    // Get user with current balance
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        creditBalance: true,
      }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Check if user already owns this content
    const existingPurchase = await prisma.contentPurchase.findUnique({
      where: {
        userId_contentType_contentId: {
          userId: session.user.id,
          contentType: contentType as ContentType,
          contentId,
        }
      }
    })

    if (existingPurchase && existingPurchase.accessGranted) {
      return NextResponse.json({ error: "Content already owned" }, { status: 400 })
    }

    // Get content details and pricing
    let content: any
    let author: any
    let creditPrice: number

    if (contentType === 'NOVEL') {
      content = await prisma.novel.findUnique({
        where: { id: contentId },
        include: { author: true }
      })
      creditPrice = content?.creditPrice || 0
      author = content?.author
    } else {
      content = await prisma.chapter.findUnique({
        where: { id: contentId },
        include: { 
          novel: { 
            include: { author: true } 
          } 
        }
      })
      creditPrice = content?.creditPrice || 0
      author = content?.novel?.author
    }

    if (!content) {
      return NextResponse.json({ error: "Content not found" }, { status: 404 })
    }

    if (!creditPrice || creditPrice <= 0) {
      return NextResponse.json({ error: "Content is not available for credit purchase" }, { status: 400 })
    }

    // Check if user has enough credits
    if (user.creditBalance < creditPrice) {
      return NextResponse.json({ 
        error: "Insufficient credits",
        required: creditPrice,
        current: user.creditBalance
      }, { status: 400 })
    }

    // Calculate revenue split (assuming 1 credit = $0.10 for revenue calculation)
    const dollarValue = creditPrice * 0.10
    const { platformFee, authorEarning } = calculateRevenueSplit(dollarValue)

    const newBalance = user.creditBalance - creditPrice

    // Process the purchase in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update user credit balance
      await tx.user.update({
        where: { id: user.id },
        data: { creditBalance: newBalance }
      })

      // Create content purchase record
      const contentPurchase = await tx.contentPurchase.create({
        data: {
          userId: user.id,
          contentType: contentType as ContentType,
          contentId,
          creditsSpent: creditPrice,
          priceAtTime: dollarValue,
          accessGranted: true,
        }
      })

      // Create credit transaction record
      await tx.creditTransaction.create({
        data: {
          userId: user.id,
          type: CreditTransactionType.SPEND,
          status: CreditTransactionStatus.COMPLETED,
          amount: -creditPrice, // Negative for spending
          description: `Unlocked ${contentType.toLowerCase()}: ${content.title}`,
          sourceType: 'content_unlock',
          sourceId: contentId,
          balanceBefore: user.creditBalance,
          balanceAfter: newBalance,
          contentPurchaseId: contentPurchase.id,
        }
      })

      // Create earning record for the author
      if (author && authorEarning > 0) {
        await tx.earning.create({
          data: {
            userId: author.id,
            type: EarningType.CREDIT_PURCHASE,
            amount: authorEarning,
            description: `Credit purchase: ${content.title}`,
            sourceType: contentType as ContentType,
            sourceId: contentId,
            contentPurchaseId: contentPurchase.id,
            platformFee,
            authorEarning,
          }
        })
      }

      return contentPurchase
    })

    // Send notifications asynchronously
    Promise.all([
      CreditNotificationService.sendContentUnlockNotification(result.id),
      // Send earnings notification if there's an author earning
      author ? CreditNotificationService.sendWriterEarningsNotification(result.id) : Promise.resolve(),
    ]).catch(error => {
      console.error('Error sending notifications:', error)
    })

    return NextResponse.json({
      message: "Content unlocked successfully",
      purchase: result,
      newBalance,
      creditsSpent: creditPrice
    })
  } catch (error) {
    console.error("Error spending credits:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
