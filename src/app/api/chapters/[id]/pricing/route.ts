import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { isContentAuthor } from "@/lib/content-access"
import { isValidCreditPrice } from "@/lib/credits"
import { z } from "zod"

const updateChapterPricingSchema = z.object({
  isPremium: z.boolean(),
  creditPrice: z.number().int().min(1).max(1000).optional(),
  requiredTier: z.enum(['FREE', 'PREMIUM', 'PREMIUM_PLUS']).optional(),
})

// PUT /api/chapters/[id]/pricing - Update chapter pricing
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const chapterId = params.id
    const body = await request.json()
    const { isPremium, creditPrice, requiredTier } = updateChapterPricingSchema.parse(body)

    // Get chapter with novel to check ownership
    const chapter = await prisma.chapter.findUnique({
      where: { id: chapterId },
      include: {
        novel: {
          select: { authorId: true }
        }
      }
    })

    if (!chapter) {
      return NextResponse.json({ error: "Chapter not found" }, { status: 404 })
    }

    // Check if user is the author
    if (!isContentAuthor(session.user, chapter.novel.authorId)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Validate credit price if provided
    if (creditPrice && !isValidCreditPrice(creditPrice)) {
      return NextResponse.json(
        { error: "Invalid credit price. Must be between 1 and 1000 credits." },
        { status: 400 }
      )
    }

    // Update chapter pricing
    const updatedChapter = await prisma.chapter.update({
      where: { id: chapterId },
      data: {
        isPremium,
        creditPrice: isPremium ? creditPrice : null,
        requiredTier: isPremium ? requiredTier : null,
      },
      include: {
        novel: {
          select: {
            id: true,
            title: true,
            authorId: true,
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      chapter: {
        id: updatedChapter.id,
        title: updatedChapter.title,
        isPremium: updatedChapter.isPremium,
        creditPrice: updatedChapter.creditPrice,
        requiredTier: updatedChapter.requiredTier,
        novel: updatedChapter.novel,
      }
    })

  } catch (error) {
    console.error("Error updating chapter pricing:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// GET /api/chapters/[id]/pricing - Get chapter pricing info
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    const chapterId = params.id

    const chapter = await prisma.chapter.findUnique({
      where: { id: chapterId },
      select: {
        id: true,
        title: true,
        isPremium: true,
        creditPrice: true,
        requiredTier: true,
        novel: {
          select: {
            id: true,
            title: true,
            authorId: true,
          }
        }
      }
    })

    if (!chapter) {
      return NextResponse.json({ error: "Chapter not found" }, { status: 404 })
    }

    // Check if user is the author for full details
    const isAuthor = session?.user && isContentAuthor(session.user, chapter.novel.authorId)

    if (!isAuthor) {
      // Return limited info for non-authors
      return NextResponse.json({
        id: chapter.id,
        title: chapter.title,
        isPremium: chapter.isPremium,
        creditPrice: chapter.creditPrice,
        requiredTier: chapter.requiredTier,
      })
    }

    // Get pricing analytics for authors
    const purchaseCount = await prisma.contentPurchase.count({
      where: {
        contentType: 'CHAPTER',
        contentId: chapterId,
      }
    })

    const totalRevenue = await prisma.contentPurchase.aggregate({
      where: {
        contentType: 'CHAPTER',
        contentId: chapterId,
      },
      _sum: {
        creditPrice: true,
      }
    })

    return NextResponse.json({
      ...chapter,
      analytics: {
        purchaseCount,
        totalCreditRevenue: totalRevenue._sum.creditPrice || 0,
      }
    })

  } catch (error) {
    console.error("Error fetching chapter pricing:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
