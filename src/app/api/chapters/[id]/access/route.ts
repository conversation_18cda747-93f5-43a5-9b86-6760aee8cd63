import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import * as ContentAccess from "@/lib/content-access"
import { SubscriptionStatus } from "@prisma/client"

// GET /api/chapters/[id]/access - Check chapter access for readers
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    const chapterId = params.id

    // Get chapter with novel info
    const chapter = await prisma.chapter.findUnique({
      where: { id: chapterId },
      select: {
        id: true,
        title: true,
        order: true,
        isPremium: true,
        creditPrice: true,
        requiredTier: true,
        status: true,
        novel: {
          select: {
            id: true,
            title: true,
            authorId: true,
            isPremium: true,
            requiredTier: true,
            creditPrice: true,
          }
        }
      }
    })

    if (!chapter) {
      return NextResponse.json({ error: "Chapter not found" }, { status: 404 })
    }

    // Check if chapter is published (unless user is author)
    const isAuthor = session?.user?.id === chapter.novel.authorId
    if (chapter.status !== 'PUBLISHED' && !isAuthor) {
      return NextResponse.json({ error: "Chapter not found" }, { status: 404 })
    }

    // If user is not authenticated, return basic access info
    if (!session?.user) {
      const isPremium = chapter.isPremium || chapter.novel.isPremium
      return NextResponse.json({
        hasAccess: !isPremium,
        chapter: {
          id: chapter.id,
          title: chapter.title,
          order: chapter.order,
          isPremium: chapter.isPremium,
          creditPrice: chapter.creditPrice,
          requiredTier: chapter.requiredTier,
        },
        novel: {
          id: chapter.novel.id,
          title: chapter.novel.title,
          isPremium: chapter.novel.isPremium,
          requiredTier: chapter.novel.requiredTier,
        },
        accessInfo: {
          hasAccess: !isPremium,
          reason: isPremium ? 'authentication_required' : undefined,
          requiresAuth: isPremium,
          creditPrice: chapter.creditPrice,
          canPurchaseWithCredits: !!(chapter.creditPrice && chapter.creditPrice > 0),
          userCreditBalance: 0,
        }
      })
    }

    // Get user with subscription and credit info
    const userWithSubscription = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        creditBalance: true,
        subscriptions: {
          where: {
            status: {
              in: [SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIALING]
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 1
        }
      }
    }) as UserWithSubscription | null

    if (!userWithSubscription) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Check if user already purchased this chapter
    const existingPurchase = await prisma.contentPurchase.findFirst({
      where: {
        userId: session.user.id,
        contentType: 'CHAPTER',
        contentId: chapterId,
      }
    })

    // If user is author, grant access
    if (isAuthor) {
      return NextResponse.json({
        hasAccess: true,
        chapter: {
          id: chapter.id,
          title: chapter.title,
          order: chapter.order,
          isPremium: chapter.isPremium,
          creditPrice: chapter.creditPrice,
          requiredTier: chapter.requiredTier,
        },
        novel: {
          id: chapter.novel.id,
          title: chapter.novel.title,
          isPremium: chapter.novel.isPremium,
          requiredTier: chapter.novel.requiredTier,
        },
        accessInfo: {
          hasAccess: true,
          accessMethod: 'author',
          userCreditBalance: userWithSubscription.creditBalance,
        }
      })
    }

    // Check chapter access
    const chapterAccessResult = checkChapterAccess(userWithSubscription, chapter, chapter.novel)

    // If user already purchased, grant access
    if (existingPurchase) {
      return NextResponse.json({
        hasAccess: true,
        chapter: {
          id: chapter.id,
          title: chapter.title,
          order: chapter.order,
          isPremium: chapter.isPremium,
          creditPrice: chapter.creditPrice,
          requiredTier: chapter.requiredTier,
        },
        novel: {
          id: chapter.novel.id,
          title: chapter.novel.title,
          isPremium: chapter.novel.isPremium,
          requiredTier: chapter.novel.requiredTier,
        },
        accessInfo: {
          hasAccess: true,
          accessMethod: 'purchased',
          userCreditBalance: userWithSubscription.creditBalance,
          hasPurchased: true,
        }
      })
    }

    // Return access result with purchase options
    const canAffordCredits = chapter.creditPrice ? userWithSubscription.creditBalance >= chapter.creditPrice : false

    return NextResponse.json({
      hasAccess: chapterAccessResult.hasAccess,
      chapter: {
        id: chapter.id,
        title: chapter.title,
        order: chapter.order,
        isPremium: chapter.isPremium,
        creditPrice: chapter.creditPrice,
        requiredTier: chapter.requiredTier,
      },
      novel: {
        id: chapter.novel.id,
        title: chapter.novel.title,
        isPremium: chapter.novel.isPremium,
        requiredTier: chapter.novel.requiredTier,
      },
      accessInfo: {
        hasAccess: chapterAccessResult.hasAccess,
        reason: chapterAccessResult.reason,
        accessMethod: chapterAccessResult.accessMethod,
        requiredTier: chapterAccessResult.requiredTier,
        currentTier: chapterAccessResult.currentTier,
        creditPrice: chapter.creditPrice,
        canPurchaseWithCredits: !!(chapter.creditPrice && chapter.creditPrice > 0),
        userCreditBalance: userWithSubscription.creditBalance,
        hasPurchased: false,
        purchaseOptions: {
          subscription: {
            available: !!chapterAccessResult.requiredTier,
            requiredTier: chapterAccessResult.requiredTier,
          },
          credits: {
            available: !!(chapter.creditPrice && chapter.creditPrice > 0),
            price: chapter.creditPrice || 0,
            canAfford: canAffordCredits,
            userBalance: userWithSubscription.creditBalance,
          }
        }
      }
    })

  } catch (error) {
    console.error("Error checking chapter access:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
