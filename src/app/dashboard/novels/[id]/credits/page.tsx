"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { AuthorGuard } from "@/components/auth/auth-guard"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { LoadingSpinner } from "@/components/common/loading-spinner"
import { useToast } from "@/hooks/use-toast"
import { ArrowLeft, Coins, BookOpen } from "lucide-react"
import Link from "next/link"
import { QuickCreditSetter } from "@/components/writer/quick-credit-setter"

interface Chapter {
  id: string
  title: string
  order: number
  status: string
  isPremium: boolean
  creditPrice?: number | null
}

interface Novel {
  id: string
  title: string
  authorId: string
}

export default function NovelCreditsPage() {
  const { data: session } = useSession()
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  
  const novelId = params.id as string
  const [novel, setNovel] = useState<Novel | null>(null)
  const [chapters, setChapters] = useState<Chapter[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (novelId) {
      loadChapters()
    }
  }, [novelId])

  const loadChapters = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const response = await fetch(`/api/novels/${novelId}/chapters/bulk-pricing`)
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Novel not found')
        } else if (response.status === 403) {
          throw new Error('You do not have permission to access this novel')
        } else {
          throw new Error('Failed to load chapters')
        }
      }
      
      const result = await response.json()
      setNovel(result.novel)
      setChapters(result.chapters || [])
    } catch (error) {
      console.error('Error loading chapters:', error)
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const handleUpdate = () => {
    loadChapters()
  }

  if (isLoading) {
    return (
      <AuthorGuard>
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center py-12">
            <LoadingSpinner size="lg" text="Loading chapters..." />
          </div>
        </div>
      </AuthorGuard>
    )
  }

  if (error) {
    return (
      <AuthorGuard>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto text-center space-y-4">
            <h1 className="text-2xl font-bold">Error</h1>
            <p className="text-muted-foreground">{error}</p>
            <div className="flex gap-2 justify-center">
              <Button onClick={() => router.back()}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Go Back
              </Button>
              <Button variant="outline" onClick={loadChapters}>
                Try Again
              </Button>
            </div>
          </div>
        </div>
      </AuthorGuard>
    )
  }

  return (
    <AuthorGuard>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Header */}
          <div className="flex items-center gap-4">
            <Link href={`/dashboard/novels/${novelId}`}>
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Novel
              </Button>
            </Link>
            <div className="flex-1">
              <h1 className="text-3xl font-bold flex items-center gap-2">
                <Coins className="h-8 w-8 text-yellow-500" />
                Set Chapter Credits
              </h1>
              <p className="text-muted-foreground">
                Configure credit pricing for "{novel?.title}"
              </p>
            </div>
          </div>

          {/* Instructions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">How to Set Credits</CardTitle>
              <CardDescription>
                Set how many credits readers need to unlock each chapter
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-medium mb-2">💡 Pricing Tips:</h4>
                  <ul className="space-y-1 text-muted-foreground">
                    <li>• Keep first 2-3 chapters free</li>
                    <li>• Standard chapters: 3-8 credits</li>
                    <li>• Special chapters: 10-15 credits</li>
                    <li>• 1 credit = $0.10 USD</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">🎯 Quick Actions:</h4>
                  <ul className="space-y-1 text-muted-foreground">
                    <li>• Click "Set Credits" to edit pricing</li>
                    <li>• Use quick buttons for common prices</li>
                    <li>• Click "Make Free" to remove paywall</li>
                    <li>• Changes save automatically</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Chapter List */}
          {chapters.length > 0 ? (
            <div className="space-y-4">
              <h2 className="text-xl font-semibold">Your Chapters</h2>
              {chapters.map((chapter) => (
                <QuickCreditSetter
                  key={chapter.id}
                  chapter={chapter}
                  onUpdate={handleUpdate}
                />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="py-12">
                <div className="text-center space-y-4">
                  <div className="mx-auto p-3 bg-muted rounded-full w-fit">
                    <BookOpen className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium">No Chapters Yet</h3>
                    <p className="text-muted-foreground">
                      Create some chapters first to set credit pricing.
                    </p>
                  </div>
                  <Button asChild>
                    <Link href={`/dashboard/novels/${novelId}/chapters/new`}>
                      Create First Chapter
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Summary */}
          {chapters.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold">{chapters.length}</div>
                    <div className="text-sm text-muted-foreground">Total Chapters</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600">
                      {chapters.filter(c => !c.isPremium).length}
                    </div>
                    <div className="text-sm text-muted-foreground">Free Chapters</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-blue-600">
                      {chapters.filter(c => c.isPremium).length}
                    </div>
                    <div className="text-sm text-muted-foreground">Premium Chapters</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </AuthorGuard>
  )
}
