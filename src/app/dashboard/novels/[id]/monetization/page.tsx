"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { AuthorGuard } from "@/components/auth/auth-guard"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { LoadingSpinner } from "@/components/common/loading-spinner"
import { useToast } from "@/hooks/use-toast"
import {
  ArrowLeft,
  DollarSign,
  Coins,
  TrendingUp,
  Users,
  Settings,
  BarChart3,
  Crown,
  Lightbulb,
  BookOpen
} from "lucide-react"
import Link from "next/link"
import { ChapterMonetizationManager } from "@/components/writer/chapter-monetization-manager"
import { formatCredits, creditsToUSD } from "@/lib/credits"

interface Novel {
  id: string
  title: string
  authorId: string
}

interface Chapter {
  id: string
  title: string
  order: number
  status: string
  isPremium: boolean
  creditPrice?: number | null
  requiredTier?: string | null
  content?: string
  analytics?: {
    purchaseCount: number
    totalCreditRevenue: number
    views: number
  }
}

interface MonetizationData {
  novel: Novel
  chapters: Chapter[]
  stats: {
    totalChapters: number
    premiumChapters: number
    freeChapters: number
    totalRevenue: number
    totalPurchases: number
    averagePrice: number
  }
  recommendations: Array<{
    type: string
    title: string
    description: string
    priority: 'high' | 'medium' | 'low'
    action?: string
    affectedChapters?: string[]
    suggestedPrice?: number
  }>
}

export default function NovelMonetizationPage() {
  const { data: session } = useSession()
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  
  const novelId = params.id as string
  const [data, setData] = useState<MonetizationData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (novelId) {
      loadMonetizationData()
    }
  }, [novelId])

  const loadMonetizationData = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const response = await fetch(`/api/novels/${novelId}/chapters/bulk-pricing`)
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Novel not found')
        } else if (response.status === 403) {
          throw new Error('You do not have permission to access this novel')
        } else {
          throw new Error('Failed to load monetization data')
        }
      }
      
      const result = await response.json()
      setData(result)
    } catch (error) {
      console.error('Error loading monetization data:', error)
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const handleUpdate = () => {
    loadMonetizationData()
  }

  if (isLoading) {
    return (
      <AuthorGuard>
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center py-12">
            <LoadingSpinner size="lg" text="Loading monetization settings..." />
          </div>
        </div>
      </AuthorGuard>
    )
  }

  if (error) {
    return (
      <AuthorGuard>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto text-center space-y-4">
            <h1 className="text-2xl font-bold">Error</h1>
            <p className="text-muted-foreground">{error}</p>
            <div className="flex gap-2 justify-center">
              <Button onClick={() => router.back()}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Go Back
              </Button>
              <Button variant="outline" onClick={loadMonetizationData}>
                Try Again
              </Button>
            </div>
          </div>
        </div>
      </AuthorGuard>
    )
  }

  if (!data) {
    return null
  }

  const { novel, chapters, stats, recommendations } = data

  return (
    <AuthorGuard>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header */}
          <div className="flex items-center gap-4">
            <Link href={`/dashboard/novels/${novelId}`}>
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Novel
              </Button>
            </Link>
            <div className="flex-1">
              <h1 className="text-3xl font-bold flex items-center gap-2">
                <Settings className="h-8 w-8 text-primary" />
                Monetization Settings
              </h1>
              <p className="text-muted-foreground">
                Configure pricing and access for "{novel.title}"
              </p>
            </div>
          </div>

          {/* Revenue Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Coins className="h-5 w-5 text-yellow-600" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                    <p className="text-2xl font-bold">
                      {formatCredits(stats.totalRevenue)}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      ${creditsToUSD(stats.totalRevenue).toFixed(2)} USD
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Purchases</p>
                    <p className="text-2xl font-bold">{stats.totalPurchases}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Crown className="h-5 w-5 text-purple-600" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Premium Chapters</p>
                    <p className="text-2xl font-bold">{stats.premiumChapters}</p>
                    <p className="text-xs text-muted-foreground">
                      of {stats.totalChapters} total
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <BarChart3 className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Average Price</p>
                    <p className="text-2xl font-bold">
                      {stats.averagePrice > 0 ? formatCredits(stats.averagePrice) : 'N/A'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recommendations */}
          {recommendations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="h-5 w-5 text-yellow-500" />
                  Monetization Recommendations
                </CardTitle>
                <CardDescription>
                  AI-powered suggestions to optimize your chapter pricing
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recommendations.map((rec, index) => (
                    <Alert key={index} className={
                      rec.priority === 'high' ? 'border-red-200 bg-red-50' :
                      rec.priority === 'medium' ? 'border-yellow-200 bg-yellow-50' :
                      'border-blue-200 bg-blue-50'
                    }>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium">{rec.title}</span>
                            <Badge variant={
                              rec.priority === 'high' ? 'destructive' :
                              rec.priority === 'medium' ? 'default' :
                              'secondary'
                            }>
                              {rec.priority}
                            </Badge>
                          </div>
                          <AlertDescription>{rec.description}</AlertDescription>
                        </div>
                        {rec.action && (
                          <Button size="sm" variant="outline">
                            Apply
                          </Button>
                        )}
                      </div>
                    </Alert>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Main Monetization Manager */}
          {chapters.length > 0 ? (
            <ChapterMonetizationManager
              novelId={novelId}
              chapters={chapters}
              onUpdate={handleUpdate}
            />
          ) : (
            <Card>
              <CardContent className="py-12">
                <div className="text-center space-y-4">
                  <div className="mx-auto p-3 bg-muted rounded-full w-fit">
                    <BookOpen className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium">No Chapters Yet</h3>
                    <p className="text-muted-foreground">
                      Create some chapters first to configure monetization settings.
                    </p>
                  </div>
                  <Button asChild>
                    <Link href={`/dashboard/novels/${novelId}/chapters/new`}>
                      Create First Chapter
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Monetization Guide */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Monetization Best Practices
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-medium">Pricing Strategy</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Keep first 2-3 chapters free to attract readers</li>
                    <li>• Use consistent pricing for similar content</li>
                    <li>• Consider 5-8 credits for standard chapters</li>
                    <li>• Price special/climax chapters higher (10-15 credits)</li>
                  </ul>
                </div>
                <div className="space-y-3">
                  <h4 className="font-medium">Reader Experience</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Provide clear value for premium content</li>
                    <li>• Use subscription tiers for loyal readers</li>
                    <li>• Preview functionality helps conversion</li>
                    <li>• Monitor analytics to optimize pricing</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AuthorGuard>
  )
}
