"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/hooks/use-toast"
import { 
  BookOpen, 
  Clock, 
  TrendingUp, 
  Calendar,
  Target,
  Award,
  BarChart3,
  Activity,
  Flame,
  Eye
} from "lucide-react"
import { format, formatDistanceToNow } from "date-fns"
import Link from "next/link"

interface ReadingStats {
  period: string
  sessionsCount: number
  totalTime: number
  chaptersRead: number
  wordsRead: number
  averageSpeed: number
  averageSessionDuration: number
  booksInProgress: number
  completionRate: number
  focusScore: number
  streakDays: number
}

interface ReadingSession {
  id: string
  novel: { id: string; title: string }
  chapter: { id: string; title: string; order: number }
  timeRead: number
  progress: number
  lastReadAt: string
}

export default function ReadingDashboardPage() {
  const { data: session, status } = useSession()
  const { toast } = useToast()
  
  const [stats, setStats] = useState<ReadingStats | null>(null)
  const [recentActivity, setRecentActivity] = useState<ReadingSession[]>([])
  const [topNovels, setTopNovels] = useState<any[]>([])
  const [dailyBreakdown, setDailyBreakdown] = useState<any[]>([])
  const [selectedPeriod, setSelectedPeriod] = useState<'day' | 'week' | 'month'>('week')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (session?.user) {
      loadReadingStats()
    }
  }, [session, selectedPeriod])

  const loadReadingStats = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/reading-sessions/stats?period=${selectedPeriod}`)
      
      if (response.ok) {
        const data = await response.json()
        setStats(data.stats)
        setRecentActivity(data.recentActivity || [])
        setTopNovels(data.topNovels || [])
        setDailyBreakdown(data.dailyBreakdown || [])
      } else {
        throw new Error('Failed to load reading stats')
      }
    } catch (error) {
      console.error("Error loading reading stats:", error)
      toast({
        title: "Error",
        description: "Failed to load reading statistics",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`
    } else {
      return `${minutes}m`
    }
  }

  const getStreakColor = (days: number) => {
    if (days >= 30) return "text-purple-600"
    if (days >= 14) return "text-blue-600"
    if (days >= 7) return "text-green-600"
    if (days >= 3) return "text-yellow-600"
    return "text-gray-600"
  }

  const getFocusScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600"
    if (score >= 60) return "text-yellow-600"
    return "text-red-600"
  }

  if (status === "loading" || isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          <div className="h-8 bg-muted rounded animate-pulse" />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded animate-pulse" />
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (status === "unauthenticated") {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <h1 className="text-2xl font-bold mb-4">Sign In Required</h1>
        <p className="text-muted-foreground mb-6">Please sign in to view your reading dashboard.</p>
        <Button asChild>
          <Link href="/auth/signin">Sign In</Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Reading Dashboard</h1>
          <p className="text-muted-foreground">Track your reading progress and habits</p>
        </div>
        <Select value={selectedPeriod} onValueChange={(value: any) => setSelectedPeriod(value)}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="day">Today</SelectItem>
            <SelectItem value="week">This Week</SelectItem>
            <SelectItem value="month">This Month</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Reading Time</p>
                <p className="text-2xl font-bold">{stats ? formatDuration(stats.totalTime) : '0m'}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <BookOpen className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Chapters Read</p>
                <p className="text-2xl font-bold">{stats?.chaptersRead || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Reading Speed</p>
                <p className="text-2xl font-bold">{stats?.averageSpeed || 0} <span className="text-sm font-normal">wpm</span></p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Flame className={`h-5 w-5 ${getStreakColor(stats?.streakDays || 0)}`} />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Reading Streak</p>
                <p className="text-2xl font-bold">{stats?.streakDays || 0} <span className="text-sm font-normal">days</span></p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Reading Performance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Focus Score</span>
                  <span className={`text-sm font-bold ${getFocusScoreColor(stats?.focusScore || 0)}`}>
                    {stats?.focusScore || 0}%
                  </span>
                </div>
                <Progress value={stats?.focusScore || 0} className="h-2" />
              </div>
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Completion Rate</span>
                  <span className="text-sm font-bold">{stats?.completionRate || 0}%</span>
                </div>
                <Progress value={stats?.completionRate || 0} className="h-2" />
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4 pt-4 border-t">
              <div className="text-center">
                <p className="text-2xl font-bold">{stats?.sessionsCount || 0}</p>
                <p className="text-sm text-muted-foreground">Sessions</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold">{stats?.booksInProgress || 0}</p>
                <p className="text-sm text-muted-foreground">Books Reading</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold">{stats ? formatDuration(stats.averageSessionDuration) : '0m'}</p>
                <p className="text-sm text-muted-foreground">Avg Session</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Top Books
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {topNovels.slice(0, 5).map((novel, index) => (
                <div key={novel.novel.id} className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{novel.novel.title}</p>
                    <p className="text-xs text-muted-foreground">
                      {formatDuration(novel.timeRead)} • {novel.chaptersRead} chapters
                    </p>
                  </div>
                  <Badge variant="outline" className="ml-2">
                    #{index + 1}
                  </Badge>
                </div>
              ))}
              {topNovels.length === 0 && (
                <p className="text-sm text-muted-foreground text-center py-4">
                  No reading activity yet
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Recent Reading Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-center justify-between p-3 bg-muted/50 rounded">
                <div className="flex-1">
                  <p className="font-medium">{activity.novel.title}</p>
                  <p className="text-sm text-muted-foreground">
                    Chapter {activity.chapter.order}: {activity.chapter.title}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {formatDistanceToNow(new Date(activity.lastReadAt), { addSuffix: true })}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">{formatDuration(activity.timeRead)}</p>
                  <p className="text-xs text-muted-foreground">{Math.round(activity.progress)}% complete</p>
                </div>
              </div>
            ))}
            {recentActivity.length === 0 && (
              <p className="text-center text-muted-foreground py-8">
                No recent reading activity
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
