import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"

interface ChapterAccessInfo {
  hasAccess: boolean
  chapter: {
    id: string
    title: string
    order: number
    isPremium: boolean
    creditPrice?: number | null
    requiredTier?: string | null
  }
  novel: {
    id: string
    title: string
    isPremium: boolean
    requiredTier?: string | null
  }
  accessInfo: {
    hasAccess: boolean
    reason?: string
    accessMethod?: string
    requiredTier?: string
    currentTier?: string
    creditPrice?: number | null
    canPurchaseWithCredits?: boolean
    userCreditBalance?: number
    hasPurchased?: boolean
    requiresAuth?: boolean
    purchaseOptions?: {
      subscription: {
        available: boolean
        requiredTier?: string
      }
      credits: {
        available: boolean
        price: number
        canAfford: boolean
        userBalance: number
      }
    }
  }
}

interface UseChapterAccessResult {
  accessInfo: ChapterAccessInfo | null
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function useChapterAccess(chapterId: string): UseChapterAccessResult {
  const { data: session, status } = useSession()
  const [accessInfo, setAccessInfo] = useState<ChapterAccessInfo | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchAccessInfo = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch(`/api/chapters/${chapterId}/access`)
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Chapter not found')
        }
        throw new Error('Failed to check chapter access')
      }

      const data = await response.json()
      setAccessInfo(data)
    } catch (err) {
      console.error('Error fetching chapter access:', err)
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (chapterId && status !== 'loading') {
      fetchAccessInfo()
    }
  }, [chapterId, status, session?.user?.id])

  const refetch = async () => {
    await fetchAccessInfo()
  }

  return {
    accessInfo,
    isLoading,
    error,
    refetch
  }
}
