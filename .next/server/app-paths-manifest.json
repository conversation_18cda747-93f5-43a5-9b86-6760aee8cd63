{"/novels/[id]/page": "app/novels/[id]/page.js", "/browse/page": "app/browse/page.js", "/api/credits/balance/route": "app/api/credits/balance/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/novels/[id]/route": "app/api/novels/[id]/route.js", "/api/chapters/[id]/pricing/route": "app/api/chapters/[id]/pricing/route.js", "/api/library/check/[novelId]/route": "app/api/library/check/[novelId]/route.js", "/api/credits/packages/route": "app/api/credits/packages/route.js", "/novels/[id]/chapters/[chapterId]/page": "app/novels/[id]/chapters/[chapterId]/page.js"}