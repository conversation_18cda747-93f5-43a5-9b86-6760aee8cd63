{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/page": "app/page.js", "/_not-found/page": "app/_not-found/page.js", "/browse/page": "app/browse/page.js", "/dashboard/novels/[id]/page": "app/dashboard/novels/[id]/page.js", "/api/novels/[id]/route": "app/api/novels/[id]/route.js", "/api/library/check/[novelId]/route": "app/api/library/check/[novelId]/route.js", "/novels/[id]/page": "app/novels/[id]/page.js", "/api/library/route": "app/api/library/route.js", "/api/chapters/[id]/publish/route": "app/api/chapters/[id]/publish/route.js", "/novels/[id]/chapters/[chapterId]/page": "app/novels/[id]/chapters/[chapterId]/page.js", "/api/chapters/[id]/route": "app/api/chapters/[id]/route.js"}