{"/dashboard/novels/[id]/page": "app/dashboard/novels/[id]/page.js", "/dashboard/page": "app/dashboard/page.js", "/_not-found/page": "app/_not-found/page.js", "/auth/signin/page": "app/auth/signin/page.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/novels/[id]/route": "app/api/novels/[id]/route.js", "/unauthorized/page": "app/unauthorized/page.js", "/page": "app/page.js", "/api/novels/route": "app/api/novels/route.js", "/api/library/check/[novelId]/route": "app/api/library/check/[novelId]/route.js", "/browse/page": "app/browse/page.js", "/novels/[id]/page": "app/novels/[id]/page.js", "/api/chapters/[id]/pricing/route": "app/api/chapters/[id]/pricing/route.js", "/api/credits/balance/route": "app/api/credits/balance/route.js"}