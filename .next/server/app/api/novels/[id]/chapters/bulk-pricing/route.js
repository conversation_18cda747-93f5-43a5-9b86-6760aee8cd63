"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/novels/[id]/chapters/bulk-pricing/route";
exports.ids = ["app/api/novels/[id]/chapters/bulk-pricing/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "./action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Fbulk-pricing%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Fbulk-pricing%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Fbulk-pricing%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Fbulk-pricing%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Fbulk-pricing%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Fbulk-pricing%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_id_chapters_bulk_pricing_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/novels/[id]/chapters/bulk-pricing/route.ts */ \"(rsc)/./src/app/api/novels/[id]/chapters/bulk-pricing/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/novels/[id]/chapters/bulk-pricing/route\",\n        pathname: \"/api/novels/[id]/chapters/bulk-pricing\",\n        filename: \"route\",\n        bundlePath: \"app/api/novels/[id]/chapters/bulk-pricing/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/app/api/novels/[id]/chapters/bulk-pricing/route.ts\",\n    nextConfigOutput,\n    userland: _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_id_chapters_bulk_pricing_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/novels/[id]/chapters/bulk-pricing/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Fbulk-pricing%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Fbulk-pricing%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Fbulk-pricing%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/novels/[id]/chapters/bulk-pricing/route.ts":
/*!****************************************************************!*\
  !*** ./src/app/api/novels/[id]/chapters/bulk-pricing/route.ts ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _lib_content_access__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/content-access */ \"(rsc)/./src/lib/content-access.ts\");\n/* harmony import */ var _lib_credits__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/credits */ \"(rsc)/./src/lib/credits.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n\n\n\n\n\n\n\nconst bulkPricingSchema = zod__WEBPACK_IMPORTED_MODULE_6__.object({\n    selectedChapters: zod__WEBPACK_IMPORTED_MODULE_6__.array(zod__WEBPACK_IMPORTED_MODULE_6__.string()).min(1, \"Select at least one chapter\"),\n    action: zod__WEBPACK_IMPORTED_MODULE_6__[\"enum\"]([\n        \"set_premium\",\n        \"set_free\",\n        \"set_price\",\n        \"set_tier\"\n    ]),\n    creditPrice: zod__WEBPACK_IMPORTED_MODULE_6__.number().int().min(1).max(1000).optional(),\n    requiredTier: zod__WEBPACK_IMPORTED_MODULE_6__[\"enum\"]([\n        \"PREMIUM\",\n        \"PREMIUM_PLUS\"\n    ]).optional()\n});\n// PUT /api/novels/[id]/chapters/bulk-pricing - Bulk update chapter pricing\nasync function PUT(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const novelId = params.id;\n        const body = await request.json();\n        const { selectedChapters, action, creditPrice, requiredTier } = bulkPricingSchema.parse(body);\n        // Verify novel exists and user is the author\n        const novel = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.novel.findUnique({\n            where: {\n                id: novelId\n            },\n            select: {\n                authorId: true\n            }\n        });\n        if (!novel) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Novel not found\"\n            }, {\n                status: 404\n            });\n        }\n        if (!(0,_lib_content_access__WEBPACK_IMPORTED_MODULE_4__.isContentAuthor)(session.user, novel.authorId)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Forbidden\"\n            }, {\n                status: 403\n            });\n        }\n        // Verify all selected chapters belong to this novel\n        const chapters = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.chapter.findMany({\n            where: {\n                id: {\n                    in: selectedChapters\n                },\n                novelId: novelId\n            },\n            select: {\n                id: true,\n                title: true,\n                order: true\n            }\n        });\n        if (chapters.length !== selectedChapters.length) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Some chapters not found or don't belong to this novel\"\n            }, {\n                status: 400\n            });\n        }\n        // Prepare update data based on action\n        let updateData = {};\n        switch(action){\n            case \"set_free\":\n                updateData = {\n                    isPremium: false,\n                    creditPrice: null,\n                    requiredTier: null\n                };\n                break;\n            case \"set_premium\":\n                updateData = {\n                    isPremium: true,\n                    creditPrice: creditPrice || 5,\n                    requiredTier: requiredTier || \"PREMIUM\"\n                };\n                break;\n            case \"set_price\":\n                if (!creditPrice || !(0,_lib_credits__WEBPACK_IMPORTED_MODULE_5__.isValidCreditPrice)(creditPrice)) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: \"Valid credit price is required\"\n                    }, {\n                        status: 400\n                    });\n                }\n                updateData = {\n                    isPremium: true,\n                    creditPrice: creditPrice,\n                    requiredTier: requiredTier || \"PREMIUM\"\n                };\n                break;\n            case \"set_tier\":\n                if (!requiredTier) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: \"Required tier is required\"\n                    }, {\n                        status: 400\n                    });\n                }\n                updateData = {\n                    isPremium: true,\n                    requiredTier: requiredTier,\n                    // Keep existing credit price if set, otherwise use default\n                    creditPrice: creditPrice || 5\n                };\n                break;\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Invalid action\"\n                }, {\n                    status: 400\n                });\n        }\n        // Perform bulk update\n        const updateResult = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.chapter.updateMany({\n            where: {\n                id: {\n                    in: selectedChapters\n                },\n                novelId: novelId\n            },\n            data: updateData\n        });\n        // Get updated chapters for response\n        const updatedChapters = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.chapter.findMany({\n            where: {\n                id: {\n                    in: selectedChapters\n                },\n                novelId: novelId\n            },\n            select: {\n                id: true,\n                title: true,\n                order: true,\n                isPremium: true,\n                creditPrice: true,\n                requiredTier: true\n            },\n            orderBy: {\n                order: \"asc\"\n            }\n        });\n        // Log the bulk update for analytics\n        await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.$executeRaw`\n      INSERT INTO chapter_pricing_logs (user_id, novel_id, action, chapter_count, created_at)\n      VALUES (${session.user.id}, ${novelId}, ${action}, ${updateResult.count}, NOW())\n      ON CONFLICT DO NOTHING\n    `.catch(()=>{\n        // Ignore if table doesn't exist - this is optional logging\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            updatedCount: updateResult.count,\n            action: action,\n            chapters: updatedChapters,\n            message: `Successfully updated ${updateResult.count} chapters`\n        });\n    } catch (error) {\n        console.error(\"Error updating chapter pricing:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// GET /api/novels/[id]/chapters/bulk-pricing - Get pricing overview for novel\nasync function GET(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const novelId = params.id;\n        // Verify novel exists and user is the author\n        const novel = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.novel.findUnique({\n            where: {\n                id: novelId\n            },\n            select: {\n                id: true,\n                title: true,\n                authorId: true\n            }\n        });\n        if (!novel) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Novel not found\"\n            }, {\n                status: 404\n            });\n        }\n        if (!(0,_lib_content_access__WEBPACK_IMPORTED_MODULE_4__.isContentAuthor)(session.user, novel.authorId)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Forbidden\"\n            }, {\n                status: 403\n            });\n        }\n        // Get chapters with pricing info and analytics\n        const chapters = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.chapter.findMany({\n            where: {\n                novelId\n            },\n            select: {\n                id: true,\n                title: true,\n                order: true,\n                status: true,\n                isPremium: true,\n                creditPrice: true,\n                requiredTier: true,\n                createdAt: true,\n                content: true\n            },\n            orderBy: {\n                order: \"asc\"\n            }\n        });\n        // Get analytics for each chapter (only if chapters exist)\n        const chapterAnalytics = chapters.length > 0 ? await Promise.all(chapters.map(async (chapter)=>{\n            // Get purchase count\n            const purchaseCount = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.contentPurchase.count({\n                where: {\n                    contentType: \"CHAPTER\",\n                    contentId: chapter.id\n                }\n            });\n            // Get total revenue\n            const revenueResult = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.contentPurchase.aggregate({\n                where: {\n                    contentType: \"CHAPTER\",\n                    contentId: chapter.id\n                },\n                _sum: {\n                    creditsSpent: true\n                }\n            });\n            // Estimate views (you might have a separate views table)\n            const views = purchaseCount * 10 // Rough estimate: 10 views per purchase\n            ;\n            return {\n                chapterId: chapter.id,\n                purchaseCount,\n                totalCreditRevenue: revenueResult._sum.creditsSpent || 0,\n                views\n            };\n        })) : [];\n        // Combine chapters with analytics\n        const chaptersWithAnalytics = chapters.map((chapter)=>{\n            const analytics = chapterAnalytics.find((a)=>a.chapterId === chapter.id);\n            return {\n                ...chapter,\n                analytics: analytics ? {\n                    purchaseCount: analytics.purchaseCount,\n                    totalCreditRevenue: analytics.totalCreditRevenue,\n                    views: analytics.views\n                } : undefined\n            };\n        });\n        // Calculate overview stats\n        const stats = {\n            totalChapters: chapters.length,\n            premiumChapters: chapters.filter((c)=>c.isPremium).length,\n            freeChapters: chapters.filter((c)=>!c.isPremium).length,\n            totalRevenue: chapterAnalytics.reduce((sum, a)=>sum + a.totalCreditRevenue, 0),\n            totalPurchases: chapterAnalytics.reduce((sum, a)=>sum + a.purchaseCount, 0),\n            averagePrice: chapters.filter((c)=>c.isPremium && c.creditPrice).length > 0 ? Math.round(chapters.filter((c)=>c.isPremium && c.creditPrice).reduce((sum, c)=>sum + (c.creditPrice || 0), 0) / chapters.filter((c)=>c.isPremium && c.creditPrice).length) : 0\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            novel: {\n                id: novel.id,\n                title: novel.title\n            },\n            chapters: chaptersWithAnalytics,\n            stats,\n            recommendations: generatePricingRecommendations(chaptersWithAnalytics)\n        });\n    } catch (error) {\n        console.error(\"Error fetching chapter pricing overview:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Helper function to generate pricing recommendations\nfunction generatePricingRecommendations(chapters) {\n    const recommendations = [];\n    // Check for early chapters that should be free\n    const earlyPremiumChapters = chapters.filter((c)=>c.order <= 3 && c.isPremium).length;\n    if (earlyPremiumChapters > 0) {\n        recommendations.push({\n            type: \"early_chapters_free\",\n            title: \"Consider Making Early Chapters Free\",\n            description: `You have ${earlyPremiumChapters} premium chapters in the first 3. Consider making them free to attract readers.`,\n            priority: \"high\",\n            action: \"set_free\",\n            affectedChapters: chapters.filter((c)=>c.order <= 3 && c.isPremium).map((c)=>c.id)\n        });\n    }\n    // Check for pricing consistency\n    const premiumChapters = chapters.filter((c)=>c.isPremium && c.creditPrice);\n    const prices = premiumChapters.map((c)=>c.creditPrice);\n    const uniquePrices = [\n        ...new Set(prices)\n    ];\n    if (uniquePrices.length > 3) {\n        recommendations.push({\n            type: \"price_consistency\",\n            title: \"Inconsistent Pricing\",\n            description: `You have ${uniquePrices.length} different prices. Consider standardizing for better user experience.`,\n            priority: \"medium\",\n            suggestedPrice: Math.round(prices.reduce((sum, p)=>sum + p, 0) / prices.length)\n        });\n    }\n    return recommendations;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/novels/[id]/chapters/bulk-pricing/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        })\n    ],\n    callbacks: {\n        async session ({ session, user }) {\n            if (session.user) {\n                session.user.id = user.id;\n                session.user.role = user.role || _prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserRole.READER;\n            }\n            return session;\n        },\n        async signIn ({ user, account, profile }) {\n            return true;\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    session: {\n        strategy: \"database\"\n    },\n    events: {\n        async signIn ({ user, account, profile }) {\n            console.log(\"User signed in:\", {\n                user: user.email,\n                provider: account?.provider\n            });\n        },\n        async signOut ({ session }) {\n            console.log(\"User signed out:\", session?.user?.email);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/content-access.ts":
/*!***********************************!*\
  !*** ./src/lib/content-access.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkChapterAccess: () => (/* binding */ checkChapterAccess),\n/* harmony export */   checkContentAccess: () => (/* binding */ checkContentAccess),\n/* harmony export */   checkNovelAccess: () => (/* binding */ checkNovelAccess),\n/* harmony export */   getAccessReasonMessage: () => (/* binding */ getAccessReasonMessage),\n/* harmony export */   getContentAccessInfo: () => (/* binding */ getContentAccessInfo),\n/* harmony export */   getTierDisplayName: () => (/* binding */ getTierDisplayName),\n/* harmony export */   hasContentPurchase: () => (/* binding */ hasContentPurchase),\n/* harmony export */   isContentAuthor: () => (/* binding */ isContentAuthor)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\n/**\n * Check if a user has access to premium content based on their subscription or credit purchases\n */ function checkContentAccess(user, content) {\n    // If content is not premium, everyone has access\n    if (!content.isPremium) {\n        return {\n            hasAccess: true,\n            accessMethod: \"free\"\n        };\n    }\n    // If user is not logged in, check if credit purchase is available\n    if (!user) {\n        return {\n            hasAccess: false,\n            reason: \"no_subscription\",\n            requiredTier: content.requiredTier || _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.PREMIUM,\n            creditPrice: content.creditPrice || undefined,\n            canPurchaseWithCredits: !!(content.creditPrice && content.creditPrice > 0)\n        };\n    }\n    // Check if user has already purchased this content with credits\n    if (content.id && content.contentType && user.contentPurchases) {\n        const existingPurchase = user.contentPurchases.find((purchase)=>purchase.contentType === content.contentType && purchase.contentId === content.id && purchase.accessGranted);\n        if (existingPurchase) {\n            return {\n                hasAccess: true,\n                accessMethod: \"credit_purchase\"\n            };\n        }\n    }\n    // Get user's active subscription\n    const activeSubscription = user.subscriptions.find((sub)=>sub.status === _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionStatus.ACTIVE || sub.status === _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionStatus.TRIALING);\n    // If no active subscription, check if credit purchase is available\n    if (!activeSubscription) {\n        const canPurchaseWithCredits = !!(content.creditPrice && content.creditPrice > 0);\n        const hasEnoughCredits = canPurchaseWithCredits && user.creditBalance >= content.creditPrice;\n        return {\n            hasAccess: false,\n            reason: \"no_subscription\",\n            requiredTier: content.requiredTier || _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.PREMIUM,\n            creditPrice: content.creditPrice || undefined,\n            canPurchaseWithCredits,\n            userCreditBalance: user.creditBalance\n        };\n    }\n    // Check if subscription tier is sufficient\n    const requiredTier = content.requiredTier || _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.PREMIUM;\n    const userTier = activeSubscription.tier;\n    // Define tier hierarchy\n    const tierHierarchy = {\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.FREE]: 0,\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.PREMIUM]: 1,\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.PREMIUM_PLUS]: 2\n    };\n    const userTierLevel = tierHierarchy[userTier];\n    const requiredTierLevel = tierHierarchy[requiredTier];\n    if (userTierLevel < requiredTierLevel) {\n        const canPurchaseWithCredits = !!(content.creditPrice && content.creditPrice > 0);\n        return {\n            hasAccess: false,\n            reason: \"insufficient_tier\",\n            requiredTier,\n            currentTier: userTier,\n            creditPrice: content.creditPrice || undefined,\n            canPurchaseWithCredits,\n            userCreditBalance: user.creditBalance\n        };\n    }\n    return {\n        hasAccess: true,\n        accessMethod: \"subscription\",\n        currentTier: userTier\n    };\n}\n/**\n * Check if a user can access a specific novel\n */ function checkNovelAccess(user, novel) {\n    return checkContentAccess(user, {\n        isPremium: novel.isPremium,\n        requiredTier: novel.requiredTier,\n        creditPrice: novel.creditPrice,\n        id: novel.id,\n        contentType: _prisma_client__WEBPACK_IMPORTED_MODULE_0__.ContentType.NOVEL\n    });\n}\n/**\n * Check if a user can access a specific chapter\n */ function checkChapterAccess(user, chapter, novel) {\n    // First check chapter-level restrictions\n    const chapterAccess = checkContentAccess(user, {\n        isPremium: chapter.isPremium,\n        requiredTier: chapter.requiredTier,\n        creditPrice: chapter.creditPrice,\n        id: chapter.id,\n        contentType: _prisma_client__WEBPACK_IMPORTED_MODULE_0__.ContentType.CHAPTER\n    });\n    // If chapter access is granted, return that result\n    if (chapterAccess.hasAccess) {\n        return chapterAccess;\n    }\n    // If novel is provided, also check novel-level restrictions\n    if (novel) {\n        const novelAccess = checkContentAccess(user, {\n            isPremium: novel.isPremium,\n            requiredTier: novel.requiredTier,\n            creditPrice: novel.creditPrice,\n            id: novel.id,\n            contentType: _prisma_client__WEBPACK_IMPORTED_MODULE_0__.ContentType.NOVEL\n        });\n        // If novel access is granted, user can access chapter\n        if (novelAccess.hasAccess) {\n            return novelAccess;\n        }\n        // If both chapter and novel access are denied, return the more specific error\n        // Prefer chapter-level access info if it has credit purchase options\n        if (chapterAccess.canPurchaseWithCredits && !novelAccess.canPurchaseWithCredits) {\n            return chapterAccess;\n        }\n        return novelAccess;\n    }\n    return chapterAccess;\n}\n/**\n * Check if a user has purchased specific content with credits\n */ function hasContentPurchase(user, contentType, contentId) {\n    if (!user?.contentPurchases) return false;\n    return user.contentPurchases.some((purchase)=>purchase.contentType === contentType && purchase.contentId === contentId && purchase.accessGranted && (!purchase.expiresAt || purchase.expiresAt > new Date()));\n}\n/**\n * Get comprehensive access information for content including all access methods\n */ function getContentAccessInfo(user, content) {\n    const accessResult = checkContentAccess(user, content);\n    const hasPurchased = hasContentPurchase(user, content.contentType, content.id);\n    return {\n        ...accessResult,\n        hasPurchased,\n        purchaseOptions: {\n            subscription: {\n                available: !accessResult.hasAccess && accessResult.reason === \"no_subscription\",\n                requiredTier: accessResult.requiredTier\n            },\n            credits: {\n                available: !!(content.creditPrice && content.creditPrice > 0),\n                price: content.creditPrice,\n                canAfford: user ? user.creditBalance >= (content.creditPrice || 0) : false,\n                userBalance: user?.creditBalance || 0\n            }\n        }\n    };\n}\n/**\n * Get the display name for a subscription tier\n */ function getTierDisplayName(tier) {\n    switch(tier){\n        case _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.FREE:\n            return \"Free\";\n        case _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.PREMIUM:\n            return \"Premium\";\n        case _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.PREMIUM_PLUS:\n            return \"Premium Plus\";\n        default:\n            return tier;\n    }\n}\n/**\n * Get the access reason message for display\n */ function getAccessReasonMessage(result) {\n    if (result.hasAccess) {\n        return \"You have access to this content\";\n    }\n    switch(result.reason){\n        case \"no_subscription\":\n            return `This content requires a ${getTierDisplayName(result.requiredTier)} subscription`;\n        case \"insufficient_tier\":\n            return `This content requires ${getTierDisplayName(result.requiredTier)} or higher. You currently have ${getTierDisplayName(result.currentTier)}`;\n        case \"subscription_inactive\":\n            return \"Your subscription is not active. Please check your billing information\";\n        default:\n            return \"You do not have access to this premium content\";\n    }\n}\n/**\n * Check if user is an author of the content (authors can always access their own content)\n */ function isContentAuthor(user, authorId) {\n    return user?.id === authorId;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/content-access.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/credits.ts":
/*!****************************!*\
  !*** ./src/lib/credits.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CREDIT_CONFIG: () => (/* binding */ CREDIT_CONFIG),\n/* harmony export */   DEFAULT_CREDIT_PACKAGES: () => (/* binding */ DEFAULT_CREDIT_PACKAGES),\n/* harmony export */   calculateCreditRevenueSplit: () => (/* binding */ calculateCreditRevenueSplit),\n/* harmony export */   creditsToUSD: () => (/* binding */ creditsToUSD),\n/* harmony export */   formatCreditPrice: () => (/* binding */ formatCreditPrice),\n/* harmony export */   formatCredits: () => (/* binding */ formatCredits),\n/* harmony export */   getRecommendedCreditPrice: () => (/* binding */ getRecommendedCreditPrice),\n/* harmony export */   isValidCreditPrice: () => (/* binding */ isValidCreditPrice),\n/* harmony export */   usdToCredits: () => (/* binding */ usdToCredits),\n/* harmony export */   validateCreditPurchase: () => (/* binding */ validateCreditPurchase)\n/* harmony export */ });\n// Credit system configuration and utilities\nconst CREDIT_CONFIG = {\n    // Credit to USD conversion rate (1 credit = $0.10)\n    CREDIT_TO_USD_RATE: 0.10,\n    // Default credit prices for content\n    DEFAULT_CHAPTER_CREDITS: 5,\n    DEFAULT_NOVEL_CREDITS: 50,\n    // Revenue sharing for credit purchases\n    PLATFORM_PERCENTAGE: 30,\n    AUTHOR_PERCENTAGE: 70\n};\n// Default credit packages\nconst DEFAULT_CREDIT_PACKAGES = [\n    {\n        name: \"Starter Pack\",\n        description: \"Perfect for trying out premium content\",\n        credits: 50,\n        price: 4.99,\n        bonusCredits: 0,\n        sortOrder: 1\n    },\n    {\n        name: \"Value Pack\",\n        description: \"Great value for regular readers\",\n        credits: 120,\n        price: 9.99,\n        bonusCredits: 10,\n        sortOrder: 2\n    },\n    {\n        name: \"Premium Pack\",\n        description: \"Best value for avid readers\",\n        credits: 300,\n        price: 19.99,\n        bonusCredits: 50,\n        sortOrder: 3\n    },\n    {\n        name: \"Ultimate Pack\",\n        description: \"Maximum value for power readers\",\n        credits: 600,\n        price: 34.99,\n        bonusCredits: 150,\n        sortOrder: 4\n    }\n];\n// Helper functions\nconst formatCredits = (credits)=>{\n    return `${credits.toLocaleString()} credits`;\n};\nconst creditsToUSD = (credits)=>{\n    return credits * CREDIT_CONFIG.CREDIT_TO_USD_RATE;\n};\nconst usdToCredits = (usd)=>{\n    return Math.round(usd / CREDIT_CONFIG.CREDIT_TO_USD_RATE);\n};\nconst calculateCreditRevenueSplit = (credits)=>{\n    const dollarValue = creditsToUSD(credits);\n    const platformFee = dollarValue * CREDIT_CONFIG.PLATFORM_PERCENTAGE / 100;\n    const authorEarning = dollarValue - platformFee;\n    return {\n        dollarValue,\n        platformFee,\n        authorEarning\n    };\n};\n// Content pricing helpers\nconst getRecommendedCreditPrice = (contentType, wordCount)=>{\n    if (contentType === \"CHAPTER\") {\n        // Base price for chapters, can be adjusted based on word count\n        if (wordCount) {\n            // 1 credit per 500 words, minimum 3 credits\n            return Math.max(3, Math.ceil(wordCount / 500));\n        }\n        return CREDIT_CONFIG.DEFAULT_CHAPTER_CREDITS;\n    } else {\n        // Base price for novels, can be adjusted based on total word count or chapter count\n        if (wordCount) {\n            // 1 credit per 1000 words, minimum 20 credits\n            return Math.max(20, Math.ceil(wordCount / 1000));\n        }\n        return CREDIT_CONFIG.DEFAULT_NOVEL_CREDITS;\n    }\n};\nconst formatCreditPrice = (credits)=>{\n    const usdValue = creditsToUSD(credits);\n    return `${formatCredits(credits)} ($${usdValue.toFixed(2)})`;\n};\n// Validation helpers\nconst isValidCreditPrice = (credits)=>{\n    return credits > 0 && credits <= 1000 && Number.isInteger(credits);\n};\nconst validateCreditPurchase = (userBalance, requiredCredits)=>{\n    if (userBalance >= requiredCredits) {\n        return {\n            canPurchase: true\n        };\n    }\n    return {\n        canPurchase: false,\n        shortfall: requiredCredits - userBalance\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/credits.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9zcmMvbGliL2RiLnRzPzllNGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Fbulk-pricing%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Fbulk-pricing%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Fbulk-pricing%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();