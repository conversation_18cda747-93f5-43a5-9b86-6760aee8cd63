"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/novels/[id]/route";
exports.ids = ["app/api/novels/[id]/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "./action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/novels/[id]/route.ts */ \"(rsc)/./src/app/api/novels/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/novels/[id]/route\",\n        pathname: \"/api/novels/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/novels/[id]/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/app/api/novels/[id]/route.ts\",\n    nextConfigOutput,\n    userland: _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/novels/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/novels/[id]/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/novels/[id]/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_api_middleware__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api-middleware */ \"(rsc)/./src/lib/api-middleware.ts\");\n\n\n\n\n\n\nasync function GET(request, { params }) {\n    const { id } = params;\n    return (0,_lib_api_middleware__WEBPACK_IMPORTED_MODULE_5__.withNovelAccess)(request, async ()=>{\n        try {\n            // Get session first to determine if user is author\n            const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n            const novel = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.novel.findUnique({\n                where: {\n                    id\n                },\n                include: {\n                    author: {\n                        select: {\n                            id: true,\n                            name: true,\n                            image: true,\n                            bio: true\n                        }\n                    }\n                }\n            });\n            if (!novel) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Novel not found\"\n                }, {\n                    status: 404\n                });\n            }\n            const isAuthor = session?.user?.id === novel.authorId;\n            // Get chapters with appropriate filtering\n            const chapters = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.chapter.findMany({\n                where: {\n                    novelId: id,\n                    ...isAuthor ? {} : {\n                        status: \"PUBLISHED\"\n                    }\n                },\n                select: {\n                    id: true,\n                    title: true,\n                    order: true,\n                    status: true,\n                    createdAt: true,\n                    isPremium: true,\n                    requiredTier: true\n                },\n                orderBy: {\n                    order: \"asc\"\n                }\n            });\n            // Add chapters to novel object\n            const novelWithChapters = {\n                ...novel,\n                chapters,\n                _count: {\n                    chapters: chapters.length\n                }\n            };\n            if (novel.status !== _prisma_client__WEBPACK_IMPORTED_MODULE_4__.NovelStatus.PUBLISHED && !isAuthor) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Novel not found\"\n                }, {\n                    status: 404\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(novelWithChapters);\n        } catch (error) {\n            console.error(\"Error fetching novel:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to fetch novel\"\n            }, {\n                status: 500\n            });\n        }\n    }, id);\n}\nasync function PUT(request, { params }) {\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n    if (!session?.user || session.user.role !== \"AUTHOR\") {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Unauthorized\"\n        }, {\n            status: 401\n        });\n    }\n    const { id } = params;\n    try {\n        // Verify ownership\n        const existingNovel = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.novel.findUnique({\n            where: {\n                id\n            },\n            select: {\n                authorId: true\n            }\n        });\n        if (!existingNovel) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Novel not found\"\n            }, {\n                status: 404\n            });\n        }\n        if (existingNovel.authorId !== session.user.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Forbidden\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const { title, description, synopsis, genre, tags, status } = body;\n        const updatedNovel = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.novel.update({\n            where: {\n                id\n            },\n            data: {\n                ...title && {\n                    title: title.trim()\n                },\n                ...description !== undefined && {\n                    description: description?.trim() || null\n                },\n                ...synopsis !== undefined && {\n                    synopsis: synopsis?.trim() || null\n                },\n                ...genre !== undefined && {\n                    genre: genre?.trim() || null\n                },\n                ...tags && {\n                    tags\n                },\n                ...status && {\n                    status\n                },\n                ...status === _prisma_client__WEBPACK_IMPORTED_MODULE_4__.NovelStatus.PUBLISHED && {\n                    publishedAt: new Date()\n                }\n            },\n            include: {\n                author: {\n                    select: {\n                        id: true,\n                        name: true,\n                        image: true\n                    }\n                },\n                _count: {\n                    select: {\n                        chapters: true\n                    }\n                }\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(updatedNovel);\n    } catch (error) {\n        console.error(\"Error updating novel:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to update novel\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request, { params }) {\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n    if (!session?.user || session.user.role !== \"AUTHOR\") {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Unauthorized\"\n        }, {\n            status: 401\n        });\n    }\n    const { id } = params;\n    try {\n        // Verify ownership\n        const existingNovel = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.novel.findUnique({\n            where: {\n                id\n            },\n            select: {\n                authorId: true\n            }\n        });\n        if (!existingNovel) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Novel not found\"\n            }, {\n                status: 404\n            });\n        }\n        if (existingNovel.authorId !== session.user.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Forbidden\"\n            }, {\n                status: 403\n            });\n        }\n        await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.novel.delete({\n            where: {\n                id\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Novel deleted successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error deleting novel:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to delete novel\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/novels/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api-middleware.ts":
/*!***********************************!*\
  !*** ./src/lib/api-middleware.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractContentId: () => (/* binding */ extractContentId),\n/* harmony export */   withChapterAccess: () => (/* binding */ withChapterAccess),\n/* harmony export */   withContentAccess: () => (/* binding */ withContentAccess),\n/* harmony export */   withNovelAccess: () => (/* binding */ withNovelAccess)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _lib_content_access__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/content-access */ \"(rsc)/./src/lib/content-access.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\n/**\n * Middleware to check content access for API routes\n */ async function withContentAccess(request, handler, options) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        const { contentId, contentType, allowAuthor = true } = options;\n        // Get content from database\n        let content;\n        let authorId;\n        if (contentType === \"novel\") {\n            content = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.novel.findUnique({\n                where: {\n                    id: contentId\n                },\n                select: {\n                    id: true,\n                    isPremium: true,\n                    requiredTier: true,\n                    authorId: true\n                }\n            });\n            authorId = content?.authorId;\n        } else {\n            content = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.chapter.findUnique({\n                where: {\n                    id: contentId\n                },\n                include: {\n                    novel: {\n                        select: {\n                            isPremium: true,\n                            requiredTier: true,\n                            authorId: true\n                        }\n                    }\n                }\n            });\n            authorId = content?.novel?.authorId;\n        }\n        if (!content) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Content not found\"\n            }, {\n                status: 404\n            });\n        }\n        // If content is not premium, allow access\n        const isPremium = contentType === \"novel\" ? content.isPremium : content.isPremium || content.novel?.isPremium;\n        if (!isPremium) {\n            return handler(request);\n        }\n        // If user is the author and author access is allowed, grant access\n        if (allowAuthor && session?.user && (0,_lib_content_access__WEBPACK_IMPORTED_MODULE_4__.isContentAuthor)(session.user, authorId)) {\n            return handler(request);\n        }\n        // Check subscription access\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Authentication required\",\n                code: \"AUTH_REQUIRED\",\n                message: \"Please sign in to access premium content\"\n            }, {\n                status: 401\n            });\n        }\n        // Get user with subscription\n        const userWithSubscription = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: session.user.id\n            },\n            include: {\n                subscriptions: {\n                    where: {\n                        status: {\n                            in: [\n                                _prisma_client__WEBPACK_IMPORTED_MODULE_5__.SubscriptionStatus.ACTIVE,\n                                _prisma_client__WEBPACK_IMPORTED_MODULE_5__.SubscriptionStatus.TRIALING\n                            ]\n                        }\n                    },\n                    orderBy: {\n                        createdAt: \"desc\"\n                    },\n                    take: 1\n                }\n            }\n        });\n        if (!userWithSubscription) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"User not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Check content access\n        const requiredTier = contentType === \"novel\" ? content.requiredTier : content.requiredTier || content.novel?.requiredTier;\n        const accessResult = (0,_lib_content_access__WEBPACK_IMPORTED_MODULE_4__.checkContentAccess)(userWithSubscription, {\n            isPremium,\n            requiredTier\n        });\n        if (!accessResult.hasAccess) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Premium content access required\",\n                code: \"PREMIUM_REQUIRED\",\n                reason: accessResult.reason,\n                requiredTier: accessResult.requiredTier,\n                currentTier: accessResult.currentTier,\n                message: \"This content requires a premium subscription\"\n            }, {\n                status: 403\n            });\n        }\n        // User has access, proceed with the request\n        return handler(request);\n    } catch (error) {\n        console.error(\"Content access middleware error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * Helper function to extract content ID from request URL\n */ function extractContentId(request, paramName = \"id\") {\n    const url = new URL(request.url);\n    const pathSegments = url.pathname.split(\"/\");\n    // Find the segment after the param name or use the last segment\n    const idIndex = pathSegments.findIndex((segment)=>segment === paramName);\n    if (idIndex !== -1 && idIndex + 1 < pathSegments.length) {\n        return pathSegments[idIndex + 1];\n    }\n    // Fallback to last segment if it looks like an ID\n    const lastSegment = pathSegments[pathSegments.length - 1];\n    if (lastSegment && lastSegment.length > 10) {\n        return lastSegment;\n    }\n    return null;\n}\n/**\n * Wrapper for novel content access\n */ async function withNovelAccess(request, handler, novelId, allowAuthor = true) {\n    return withContentAccess(request, handler, {\n        contentId: novelId,\n        contentType: \"novel\",\n        allowAuthor\n    });\n}\n/**\n * Wrapper for chapter content access\n */ async function withChapterAccess(request, handler, chapterId, allowAuthor = true) {\n    return withContentAccess(request, handler, {\n        contentId: chapterId,\n        contentType: \"chapter\",\n        allowAuthor\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api-middleware.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        })\n    ],\n    callbacks: {\n        async session ({ session, user }) {\n            if (session.user) {\n                session.user.id = user.id;\n                session.user.role = user.role || _prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserRole.READER;\n            }\n            return session;\n        },\n        async signIn ({ user, account, profile }) {\n            return true;\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    session: {\n        strategy: \"database\"\n    },\n    events: {\n        async signIn ({ user, account, profile }) {\n            console.log(\"User signed in:\", {\n                user: user.email,\n                provider: account?.provider\n            });\n        },\n        async signOut ({ session }) {\n            console.log(\"User signed out:\", session?.user?.email);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/content-access.ts":
/*!***********************************!*\
  !*** ./src/lib/content-access.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkChapterAccess: () => (/* binding */ checkChapterAccess),\n/* harmony export */   checkContentAccess: () => (/* binding */ checkContentAccess),\n/* harmony export */   checkNovelAccess: () => (/* binding */ checkNovelAccess),\n/* harmony export */   getAccessReasonMessage: () => (/* binding */ getAccessReasonMessage),\n/* harmony export */   getContentAccessInfo: () => (/* binding */ getContentAccessInfo),\n/* harmony export */   getTierDisplayName: () => (/* binding */ getTierDisplayName),\n/* harmony export */   hasContentPurchase: () => (/* binding */ hasContentPurchase),\n/* harmony export */   isContentAuthor: () => (/* binding */ isContentAuthor)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\n/**\n * Check if a user has access to premium content based on their subscription or credit purchases\n */ function checkContentAccess(user, content) {\n    // If content is not premium, everyone has access\n    if (!content.isPremium) {\n        return {\n            hasAccess: true,\n            accessMethod: \"free\"\n        };\n    }\n    // If user is not logged in, check if credit purchase is available\n    if (!user) {\n        return {\n            hasAccess: false,\n            reason: \"no_subscription\",\n            requiredTier: content.requiredTier || _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.PREMIUM,\n            creditPrice: content.creditPrice || undefined,\n            canPurchaseWithCredits: !!(content.creditPrice && content.creditPrice > 0)\n        };\n    }\n    // Check if user has already purchased this content with credits\n    if (content.id && content.contentType && user.contentPurchases) {\n        const existingPurchase = user.contentPurchases.find((purchase)=>purchase.contentType === content.contentType && purchase.contentId === content.id && purchase.accessGranted);\n        if (existingPurchase) {\n            return {\n                hasAccess: true,\n                accessMethod: \"credit_purchase\"\n            };\n        }\n    }\n    // Get user's active subscription\n    const activeSubscription = user.subscriptions.find((sub)=>sub.status === _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionStatus.ACTIVE || sub.status === _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionStatus.TRIALING);\n    // If no active subscription, check if credit purchase is available\n    if (!activeSubscription) {\n        const canPurchaseWithCredits = !!(content.creditPrice && content.creditPrice > 0);\n        const hasEnoughCredits = canPurchaseWithCredits && user.creditBalance >= content.creditPrice;\n        return {\n            hasAccess: false,\n            reason: \"no_subscription\",\n            requiredTier: content.requiredTier || _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.PREMIUM,\n            creditPrice: content.creditPrice || undefined,\n            canPurchaseWithCredits,\n            userCreditBalance: user.creditBalance\n        };\n    }\n    // Check if subscription tier is sufficient\n    const requiredTier = content.requiredTier || _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.PREMIUM;\n    const userTier = activeSubscription.tier;\n    // Define tier hierarchy\n    const tierHierarchy = {\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.FREE]: 0,\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.PREMIUM]: 1,\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.PREMIUM_PLUS]: 2\n    };\n    const userTierLevel = tierHierarchy[userTier];\n    const requiredTierLevel = tierHierarchy[requiredTier];\n    if (userTierLevel < requiredTierLevel) {\n        const canPurchaseWithCredits = !!(content.creditPrice && content.creditPrice > 0);\n        return {\n            hasAccess: false,\n            reason: \"insufficient_tier\",\n            requiredTier,\n            currentTier: userTier,\n            creditPrice: content.creditPrice || undefined,\n            canPurchaseWithCredits,\n            userCreditBalance: user.creditBalance\n        };\n    }\n    return {\n        hasAccess: true,\n        accessMethod: \"subscription\",\n        currentTier: userTier\n    };\n}\n/**\n * Check if a user can access a specific novel\n */ function checkNovelAccess(user, novel) {\n    return checkContentAccess(user, {\n        isPremium: novel.isPremium,\n        requiredTier: novel.requiredTier,\n        creditPrice: novel.creditPrice,\n        id: novel.id,\n        contentType: _prisma_client__WEBPACK_IMPORTED_MODULE_0__.ContentType.NOVEL\n    });\n}\n/**\n * Check if a user can access a specific chapter\n */ function checkChapterAccess(user, chapter, novel) {\n    // First check chapter-level restrictions\n    const chapterAccess = checkContentAccess(user, {\n        isPremium: chapter.isPremium,\n        requiredTier: chapter.requiredTier,\n        creditPrice: chapter.creditPrice,\n        id: chapter.id,\n        contentType: _prisma_client__WEBPACK_IMPORTED_MODULE_0__.ContentType.CHAPTER\n    });\n    // If chapter access is granted, return that result\n    if (chapterAccess.hasAccess) {\n        return chapterAccess;\n    }\n    // If novel is provided, also check novel-level restrictions\n    if (novel) {\n        const novelAccess = checkContentAccess(user, {\n            isPremium: novel.isPremium,\n            requiredTier: novel.requiredTier,\n            creditPrice: novel.creditPrice,\n            id: novel.id,\n            contentType: _prisma_client__WEBPACK_IMPORTED_MODULE_0__.ContentType.NOVEL\n        });\n        // If novel access is granted, user can access chapter\n        if (novelAccess.hasAccess) {\n            return novelAccess;\n        }\n        // If both chapter and novel access are denied, return the more specific error\n        // Prefer chapter-level access info if it has credit purchase options\n        if (chapterAccess.canPurchaseWithCredits && !novelAccess.canPurchaseWithCredits) {\n            return chapterAccess;\n        }\n        return novelAccess;\n    }\n    return chapterAccess;\n}\n/**\n * Check if a user has purchased specific content with credits\n */ function hasContentPurchase(user, contentType, contentId) {\n    if (!user?.contentPurchases) return false;\n    return user.contentPurchases.some((purchase)=>purchase.contentType === contentType && purchase.contentId === contentId && purchase.accessGranted && (!purchase.expiresAt || purchase.expiresAt > new Date()));\n}\n/**\n * Get comprehensive access information for content including all access methods\n */ function getContentAccessInfo(user, content) {\n    const accessResult = checkContentAccess(user, content);\n    const hasPurchased = hasContentPurchase(user, content.contentType, content.id);\n    return {\n        ...accessResult,\n        hasPurchased,\n        purchaseOptions: {\n            subscription: {\n                available: !accessResult.hasAccess && accessResult.reason === \"no_subscription\",\n                requiredTier: accessResult.requiredTier\n            },\n            credits: {\n                available: !!(content.creditPrice && content.creditPrice > 0),\n                price: content.creditPrice,\n                canAfford: user ? user.creditBalance >= (content.creditPrice || 0) : false,\n                userBalance: user?.creditBalance || 0\n            }\n        }\n    };\n}\n/**\n * Get the display name for a subscription tier\n */ function getTierDisplayName(tier) {\n    switch(tier){\n        case _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.FREE:\n            return \"Free\";\n        case _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.PREMIUM:\n            return \"Premium\";\n        case _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.PREMIUM_PLUS:\n            return \"Premium Plus\";\n        default:\n            return tier;\n    }\n}\n/**\n * Get the access reason message for display\n */ function getAccessReasonMessage(result) {\n    if (result.hasAccess) {\n        return \"You have access to this content\";\n    }\n    switch(result.reason){\n        case \"no_subscription\":\n            return `This content requires a ${getTierDisplayName(result.requiredTier)} subscription`;\n        case \"insufficient_tier\":\n            return `This content requires ${getTierDisplayName(result.requiredTier)} or higher. You currently have ${getTierDisplayName(result.currentTier)}`;\n        case \"subscription_inactive\":\n            return \"Your subscription is not active. Please check your billing information\";\n        default:\n            return \"You do not have access to this premium content\";\n    }\n}\n/**\n * Check if user is an author of the content (authors can always access their own content)\n */ function isContentAuthor(user, authorId) {\n    return user?.id === authorId;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/content-access.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9zcmMvbGliL2RiLnRzPzllNGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();