"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/novels/[id]/page",{

/***/ "(app-pages-browser)/./src/components/novel/novel-detail.tsx":
/*!***********************************************!*\
  !*** ./src/components/novel/novel-detail.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NovelDetail: function() { return /* binding */ NovelDetail; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_library_library_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/library/library-button */ \"(app-pages-browser)/./src/components/library/library-button.tsx\");\n/* harmony import */ var _components_novel_chapter_list_with_credits__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/novel/chapter-list-with-credits */ \"(app-pages-browser)/./src/components/novel/chapter-list-with-credits.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Calendar_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Calendar,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Calendar_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Calendar,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Calendar_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Calendar,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Calendar_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Calendar,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Calendar_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Calendar,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* __next_internal_client_entry_do_not_use__ NovelDetail auto */ \n\n\n\n\n\n\n\n\n\n\nfunction NovelDetail(param) {\n    let { novel } = param;\n    var _novel_chapters, _novel_author_name_, _novel_author_name, _novel_author_name_1, _novel_author_name1;\n    const firstChapter = (_novel_chapters = novel.chapters) === null || _novel_chapters === void 0 ? void 0 : _novel_chapters.find((chapter)=>chapter.order === 1);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    variant: \"ghost\",\n                    asChild: true,\n                    className: \"mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: \"/browse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Calendar_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this),\n                            \"Back to Browse\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative aspect-[3/4] w-full max-w-sm mx-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: novel.coverImage || \"/placeholder-cover.svg\",\n                                    alt: novel.title,\n                                    fill: true,\n                                    className: \"object-cover rounded-lg shadow-lg\",\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-4xl font-bold tracking-tight\",\n                                                children: novel.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4 text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                                                                className: \"h-6 w-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                                                                        src: novel.author.image || \"\",\n                                                                        alt: novel.author.name || \"\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                                        lineNumber: 83,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                                                                        className: \"text-xs\",\n                                                                        children: ((_novel_author_name = novel.author.name) === null || _novel_author_name === void 0 ? void 0 : (_novel_author_name_ = _novel_author_name[0]) === null || _novel_author_name_ === void 0 ? void 0 : _novel_author_name_.toUpperCase()) || \"A\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                                        lineNumber: 84,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                                lineNumber: 82,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                href: \"/authors/\".concat(novel.author.id),\n                                                                className: \"hover:text-foreground transition-colors\",\n                                                                children: [\n                                                                    \"by \",\n                                                                    novel.author.name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                                lineNumber: 88,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Calendar_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                                lineNumber: 97,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatDate)(novel.publishedAt || novel.createdAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                                lineNumber: 98,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: novel.status === \"PUBLISHED\" ? \"default\" : \"secondary\",\n                                                children: novel.status.toLowerCase()\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this),\n                                            novel.genre && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: \"outline\",\n                                                children: novel.genre\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Calendar_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"mr-1 h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    novel._count.chapters,\n                                                    \" chapters\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    novel.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-muted-foreground leading-relaxed\",\n                                        children: novel.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-3\",\n                                        children: [\n                                            firstChapter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                asChild: true,\n                                                size: \"lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/novels/\".concat(novel.id, \"/chapters/\").concat(firstChapter.id),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Calendar_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Start Reading\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_library_library_button__WEBPACK_IMPORTED_MODULE_7__.LibraryButton, {\n                                                novelId: novel.id,\n                                                variant: \"outline\",\n                                                size: \"lg\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this),\n                                    novel.tags && novel.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium\",\n                                                children: \"Tags\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: novel.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"text-xs\",\n                                                        children: tag\n                                                    }, tag, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this),\n                novel.synopsis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: \"Synopsis\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"prose prose-gray dark:prose-invert max-w-none\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"whitespace-pre-wrap\",\n                                    children: novel.synopsis\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_novel_chapter_list_with_credits__WEBPACK_IMPORTED_MODULE_8__.ChapterListWithCredits, {\n                    novelId: novel.id,\n                    chapters: novel.chapters || []\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Calendar_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"About the Author\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                                        className: \"h-16 w-16\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                                                src: novel.author.image || \"\",\n                                                alt: novel.author.name || \"\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                                                className: \"text-lg\",\n                                                children: ((_novel_author_name1 = novel.author.name) === null || _novel_author_name1 === void 0 ? void 0 : (_novel_author_name_1 = _novel_author_name1[0]) === null || _novel_author_name_1 === void 0 ? void 0 : _novel_author_name_1.toUpperCase()) || \"A\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-lg\",\n                                                children: novel.author.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this),\n                                            novel.author.bio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground\",\n                                                children: novel.author.bio\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground italic\",\n                                                children: \"No bio available\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/authors/\".concat(novel.author.id),\n                                                    children: \"View Profile\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-detail.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n_c = NovelDetail;\nvar _c;\n$RefreshReg$(_c, \"NovelDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/novel/novel-detail.tsx\n"));

/***/ })

});