"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/novels/[id]/chapters/[chapterId]/page",{

/***/ "(app-pages-browser)/./src/components/chapter/chapter-reader.tsx":
/*!***************************************************!*\
  !*** ./src/components/chapter/chapter-reader.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChapterReader: function() { return /* binding */ ChapterReader; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/store/api/chaptersApi */ \"(app-pages-browser)/./src/store/api/chaptersApi.ts\");\n/* harmony import */ var _components_library_library_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/library/library-button */ \"(app-pages-browser)/./src/components/library/library-button.tsx\");\n/* harmony import */ var _components_chapter_chapter_content_renderer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/chapter/chapter-content-renderer */ \"(app-pages-browser)/./src/components/chapter/chapter-content-renderer.tsx\");\n/* harmony import */ var _components_reader_reader_credit_display__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/reader/reader-credit-display */ \"(app-pages-browser)/./src/components/reader/reader-credit-display.tsx\");\n/* harmony import */ var _components_credits_content_paywall__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/credits/content-paywall */ \"(app-pages-browser)/./src/components/credits/content-paywall.tsx\");\n/* harmony import */ var _hooks_use_chapter_access__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/use-chapter-access */ \"(app-pages-browser)/./src/hooks/use-chapter-access.ts\");\n/* __next_internal_client_entry_do_not_use__ ChapterReader auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ChapterReader(param) {\n    let { chapter, novelId } = param;\n    var _chapter_novel;\n    _s();\n    const [fontSize, setFontSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(16);\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Check chapter access\n    const { accessInfo, isLoading: isLoadingAccess, refetch: refetchAccess } = (0,_hooks_use_chapter_access__WEBPACK_IMPORTED_MODULE_11__.useChapterAccess)(chapter.id);\n    // Fetch all chapters for navigation\n    const { data: chapters } = (0,_store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_6__.useGetChaptersQuery)(novelId);\n    // Find current chapter index and navigation\n    const currentChapterIndex = Array.isArray(chapters) ? chapters.findIndex((ch)=>ch.id === chapter.id) : -1;\n    const previousChapter = currentChapterIndex > 0 && Array.isArray(chapters) ? chapters[currentChapterIndex - 1] : null;\n    const nextChapter = currentChapterIndex >= 0 && Array.isArray(chapters) && currentChapterIndex < chapters.length - 1 ? chapters[currentChapterIndex + 1] : null;\n    // Load reading preferences from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedFontSize = localStorage.getItem(\"reader-font-size\");\n        const savedTheme = localStorage.getItem(\"reader-theme\");\n        if (savedFontSize) setFontSize(parseInt(savedFontSize));\n        if (savedTheme) setTheme(savedTheme);\n    }, []);\n    // Save preferences to localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        localStorage.setItem(\"reader-font-size\", fontSize.toString());\n        localStorage.setItem(\"reader-theme\", theme);\n    }, [\n        fontSize,\n        theme\n    ]);\n    const themeClasses = {\n        light: \"bg-white text-gray-900\",\n        dark: \"bg-gray-900 text-gray-100\",\n        sepia: \"bg-amber-50 text-amber-900\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen transition-colors \".concat(themeClasses[theme]),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"sticky top-0 z-50 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/novels/\".concat(novelId),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Back to Novel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"font-semibold truncate max-w-md\",\n                                                children: ((_chapter_novel = chapter.novel) === null || _chapter_novel === void 0 ? void 0 : _chapter_novel.title) || \"Novel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"Chapter \",\n                                                    chapter.order,\n                                                    \": \",\n                                                    chapter.title\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_library_library_button__WEBPACK_IMPORTED_MODULE_7__.LibraryButton, {\n                                        novelId: novelId,\n                                        variant: \"ghost\",\n                                        size: \"sm\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowSettings(!showSettings),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b bg-muted/50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto max-w-4xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap items-center gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Font Size:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setFontSize(Math.max(12, fontSize - 2)),\n                                                disabled: fontSize <= 12,\n                                                children: \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-8 text-center text-sm\",\n                                                children: fontSize\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setFontSize(Math.min(24, fontSize + 2)),\n                                                disabled: fontSize >= 24,\n                                                children: \"+\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Theme:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-1\",\n                                        children: [\n                                            \"light\",\n                                            \"dark\",\n                                            \"sepia\"\n                                        ].map((t)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: theme === t ? \"default\" : \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setTheme(t),\n                                                className: \"capitalize\",\n                                                children: t\n                                            }, t, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8 max-w-4xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                            className: \"lg:col-span-3 space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                                    className: \"text-center space-y-4 pb-8 border-b\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                variant: \"outline\",\n                                                className: \"mb-2\",\n                                                children: [\n                                                    \"Chapter \",\n                                                    chapter.order\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl md:text-4xl font-bold tracking-tight\",\n                                                children: chapter.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground\",\n                                                children: [\n                                                    \"Published \",\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(chapter.createdAt)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                isLoadingAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground mt-2\",\n                                            children: \"Checking access...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this) : accessInfo && !accessInfo.hasAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-4 border-b\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-muted-foreground\",\n                                                    children: \"Premium Content\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground mt-1\",\n                                                    children: \"This chapter requires credits or a subscription to read\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_credits_content_paywall__WEBPACK_IMPORTED_MODULE_10__.ContentPaywall, {\n                                            contentType: \"CHAPTER\",\n                                            contentId: chapter.id,\n                                            contentTitle: \"Chapter \".concat(chapter.order, \": \").concat(chapter.title),\n                                            accessInfo: accessInfo.accessInfo,\n                                            onAccessGranted: refetchAccess\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chapter_chapter_content_renderer__WEBPACK_IMPORTED_MODULE_8__.ChapterContentRenderer, {\n                                            content: chapter.content,\n                                            fontSize: fontSize,\n                                            className: \"custom-scrollbar\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                                            className: \"pt-8 border-t space-y-4\",\n                                            children: [\n                                                Array.isArray(chapters) && chapters.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground mb-2\",\n                                                            children: [\n                                                                \"Chapter \",\n                                                                currentChapterIndex + 1,\n                                                                \" of \",\n                                                                chapters.length\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-muted rounded-full h-2 max-w-xs mx-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-primary h-2 rounded-full transition-all\",\n                                                                style: {\n                                                                    width: \"\".concat((currentChapterIndex + 1) / chapters.length * 100, \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        previousChapter ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                href: \"/novels/\".concat(novelId, \"/chapters/\").concat(previousChapter.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                        lineNumber: 256,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-left\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                children: \"Previous\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                                lineNumber: 258,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: [\n                                                                                    \"Chapter \",\n                                                                                    previousChapter.order\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                                lineNumber: 259,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 17\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            disabled: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                    lineNumber: 265,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-left\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: \"Previous\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                            lineNumber: 267,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"Chapter\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                            lineNumber: 268,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                href: \"/novels/\".concat(novelId),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                        lineNumber: 275,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    \"Back to Novel\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        nextChapter ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                href: \"/novels/\".concat(novelId, \"/chapters/\").concat(nextChapter.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                children: \"Next\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                                lineNumber: 284,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: [\n                                                                                    \"Chapter \",\n                                                                                    nextChapter.order\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                                lineNumber: 285,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                        lineNumber: 283,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"ml-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 17\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            disabled: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: \"Next\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                            lineNumber: 293,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"Chapter\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                            lineNumber: 294,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"ml-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 13\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                            className: \"lg:col-span-1 space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_reader_reader_credit_display__WEBPACK_IMPORTED_MODULE_9__.ReaderCreditDisplay, {\n                                chapterInfo: accessInfo === null || accessInfo === void 0 ? void 0 : accessInfo.chapter,\n                                accessInfo: accessInfo === null || accessInfo === void 0 ? void 0 : accessInfo.accessInfo,\n                                onAccessGranted: refetchAccess\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, this);\n}\n_s(ChapterReader, \"EYFekmGq0lLBHO1KiVgwED+tDG0=\", false, function() {\n    return [\n        _hooks_use_chapter_access__WEBPACK_IMPORTED_MODULE_11__.useChapterAccess,\n        _store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_6__.useGetChaptersQuery\n    ];\n});\n_c = ChapterReader;\nvar _c;\n$RefreshReg$(_c, \"ChapterReader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chapter/chapter-reader.tsx\n"));

/***/ })

});