"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/novels/[id]/page",{

/***/ "(app-pages-browser)/./src/components/novel/chapter-list-with-credits.tsx":
/*!************************************************************!*\
  !*** ./src/components/novel/chapter-list-with-credits.tsx ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChapterListWithCredits: function() { return /* binding */ ChapterListWithCredits; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Crown_Lock_Play_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Crown,Lock,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Crown_Lock_Play_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Crown,Lock,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Crown_Lock_Play_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Crown,Lock,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Crown_Lock_Play_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Crown,Lock,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Crown_Lock_Play_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Crown,Lock,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_credits__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/credits */ \"(app-pages-browser)/./src/lib/credits.ts\");\n/* harmony import */ var _store_api_creditsApi__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/store/api/creditsApi */ \"(app-pages-browser)/./src/store/api/creditsApi.ts\");\n/* __next_internal_client_entry_do_not_use__ ChapterListWithCredits auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ChapterListWithCredits(param) {\n    let { novelId, chapters, className } = param;\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [chaptersWithPricing, setChaptersWithPricing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingPricing, setIsLoadingPricing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Get user's credit balance\n    const { data: balanceData } = (0,_store_api_creditsApi__WEBPACK_IMPORTED_MODULE_9__.useGetCreditBalanceQuery)(undefined, {\n        skip: !(session === null || session === void 0 ? void 0 : session.user)\n    });\n    var _balanceData_balance;\n    const userBalance = (_balanceData_balance = balanceData === null || balanceData === void 0 ? void 0 : balanceData.balance) !== null && _balanceData_balance !== void 0 ? _balanceData_balance : 0;\n    // Fetch pricing information for all chapters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchChapterPricing = async ()=>{\n            if (chapters.length === 0) {\n                setIsLoadingPricing(false);\n                return;\n            }\n            try {\n                setIsLoadingPricing(true);\n                // Fetch pricing for each chapter\n                const pricingPromises = chapters.map(async (chapter)=>{\n                    try {\n                        const response = await fetch(\"/api/chapters/\".concat(chapter.id, \"/pricing\"));\n                        if (response.ok) {\n                            const pricingData = await response.json();\n                            return {\n                                ...chapter,\n                                isPremium: pricingData.isPremium || false,\n                                creditPrice: pricingData.creditPrice,\n                                requiredTier: pricingData.requiredTier\n                            };\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching pricing for chapter \".concat(chapter.id, \":\"), error);\n                    }\n                    // Fallback to basic chapter data\n                    return {\n                        ...chapter,\n                        isPremium: chapter.isPremium || false,\n                        creditPrice: chapter.creditPrice,\n                        requiredTier: chapter.requiredTier\n                    };\n                });\n                const chaptersWithPricingData = await Promise.all(pricingPromises);\n                setChaptersWithPricing(chaptersWithPricingData);\n            } catch (error) {\n                console.error(\"Error fetching chapter pricing:\", error);\n                // Fallback to original chapters\n                setChaptersWithPricing(chapters.map((chapter)=>({\n                        ...chapter,\n                        isPremium: chapter.isPremium || false,\n                        creditPrice: chapter.creditPrice,\n                        requiredTier: chapter.requiredTier\n                    })));\n            } finally{\n                setIsLoadingPricing(false);\n            }\n        };\n        fetchChapterPricing();\n    }, [\n        chapters\n    ]);\n    const getChapterAccessIcon = (chapter)=>{\n        if (!chapter.isPremium) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Crown_Lock_Play_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-4 w-4 text-green-500\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                lineNumber: 118,\n                columnNumber: 14\n            }, this);\n        }\n        if (chapter.creditPrice && chapter.creditPrice > 0) {\n            const canAfford = userBalance >= chapter.creditPrice;\n            return canAfford ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Crown_Lock_Play_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-4 w-4 text-yellow-500\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                lineNumber: 124,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Crown_Lock_Play_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"h-4 w-4 text-red-500\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Crown_Lock_Play_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            className: \"h-4 w-4 text-purple-500\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n            lineNumber: 128,\n            columnNumber: 12\n        }, this);\n    };\n    const getAccessBadge = (chapter)=>{\n        if (!chapter.isPremium) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                variant: \"secondary\",\n                className: \"text-green-600\",\n                children: \"Free\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                lineNumber: 133,\n                columnNumber: 14\n            }, this);\n        }\n        if (chapter.creditPrice && chapter.creditPrice > 0) {\n            const canAfford = userBalance >= chapter.creditPrice;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                variant: canAfford ? \"secondary\" : \"destructive\",\n                children: (0,_lib_credits__WEBPACK_IMPORTED_MODULE_8__.formatCredits)(chapter.creditPrice)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this);\n        }\n        if (chapter.requiredTier) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                variant: \"outline\",\n                children: chapter.requiredTier\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                lineNumber: 146,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            variant: \"outline\",\n            children: \"Premium\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n            lineNumber: 149,\n            columnNumber: 12\n        }, this);\n    };\n    if (chapters.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: className,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                        children: \"Chapters\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                        children: \"No chapters available yet\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                lineNumber: 155,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                        children: [\n                            \"Chapters (\",\n                            chapters.length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                        children: [\n                            \"Click on any chapter to start reading\",\n                            (session === null || session === void 0 ? void 0 : session.user) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm\",\n                                children: [\n                                    \"• Your balance: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: (0,_lib_credits__WEBPACK_IMPORTED_MODULE_8__.formatCredits)(userBalance)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 31\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: isLoadingPricing ? // Loading skeletons\n                    chapters.map((chapter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 rounded-lg border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                className: \"h-5 w-3/4 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                className: \"h-4 w-1/2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                        className: \"h-6 w-16\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 17\n                            }, this)\n                        }, chapter.id, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 15\n                        }, this)) : chaptersWithPricing.map((chapter)=>{\n                        const canAccess = !chapter.isPremium || chapter.creditPrice && userBalance >= chapter.creditPrice || !chapter.creditPrice; // Free or subscription-based\n                        const ChapterContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 rounded-lg border transition-colors \".concat(canAccess ? \"hover:bg-muted/50 cursor-pointer\" : \"bg-muted/30 cursor-not-allowed opacity-75\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3 flex-1\",\n                                        children: [\n                                            getChapterAccessIcon(chapter),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium \".concat(!canAccess ? \"text-muted-foreground\" : \"\"),\n                                                        children: [\n                                                            \"Chapter \",\n                                                            chapter.order,\n                                                            \": \",\n                                                            chapter.title\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: [\n                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatDate)(chapter.createdAt),\n                                                            !canAccess && chapter.creditPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 text-red-600 font-medium\",\n                                                                children: [\n                                                                    \"• Need \",\n                                                                    (0,_lib_credits__WEBPACK_IMPORTED_MODULE_8__.formatCredits)(chapter.creditPrice - userBalance),\n                                                                    \" more\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            getAccessBadge(chapter),\n                                            !canAccess && chapter.isPremium && chapter.creditPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Crown_Lock_Play_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 text-red-500\",\n                                                title: \"Insufficient credits\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 17\n                        }, this);\n                        return canAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            href: \"/novels/\".concat(novelId, \"/chapters/\").concat(chapter.id),\n                            className: \"block\",\n                            children: ChapterContent\n                        }, chapter.id, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 17\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"block\",\n                            children: ChapterContent\n                        }, chapter.id, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 17\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/chapter-list-with-credits.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, this);\n}\n_s(ChapterListWithCredits, \"3f2NrK28OkRsU0MIvUYWAkx+Q6c=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        _store_api_creditsApi__WEBPACK_IMPORTED_MODULE_9__.useGetCreditBalanceQuery\n    ];\n});\n_c = ChapterListWithCredits;\nvar _c;\n$RefreshReg$(_c, \"ChapterListWithCredits\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/novel/chapter-list-with-credits.tsx\n"));

/***/ })

});