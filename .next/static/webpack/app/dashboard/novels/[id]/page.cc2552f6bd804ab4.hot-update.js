"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/novels/[id]/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/coins.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Coins; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.395.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Coins = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Coins\", [\n    [\n        \"circle\",\n        {\n            cx: \"8\",\n            cy: \"8\",\n            r: \"6\",\n            key: \"3yglwk\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18.09 10.37A6 6 0 1 1 10.34 18\",\n            key: \"t5s6rm\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M7 6h1v4\",\n            key: \"1obek4\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m16.71 13.88.7.71-2.82 2.82\",\n            key: \"1rbuyh\"\n        }\n    ]\n]);\n //# sourceMappingURL=coins.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/novel/novel-management.tsx":
/*!***************************************************!*\
  !*** ./src/components/novel/novel-management.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NovelManagement: function() { return /* binding */ NovelManagement; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_common_loading_spinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/common/loading-spinner */ \"(app-pages-browser)/./src/components/common/loading-spinner.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./src/components/ui/alert-dialog.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Coins,DollarSign,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Coins,DollarSign,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Coins,DollarSign,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Coins,DollarSign,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Coins,DollarSign,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Coins,DollarSign,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Coins,DollarSign,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Coins,DollarSign,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Coins,DollarSign,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Coins,DollarSign,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Coins,DollarSign,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Coins,DollarSign,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/store/api/chaptersApi */ \"(app-pages-browser)/./src/store/api/chaptersApi.ts\");\n/* __next_internal_client_entry_do_not_use__ NovelManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NovelManagement(param) {\n    let { novel } = param;\n    var _novel__count, _novel_chapters, _novel__count1, _novel_chapters1;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUpdatingStatus, setIsUpdatingStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishChapter] = (0,_store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_12__.usePublishChapterMutation)();\n    const handlePublishChapter = async (chapterId, currentStatus)=>{\n        try {\n            const newStatus = currentStatus === \"PUBLISHED\" ? \"DRAFT\" : \"PUBLISHED\";\n            await publishChapter({\n                id: chapterId,\n                status: newStatus\n            }).unwrap();\n            toast({\n                title: \"Success\",\n                description: \"Chapter \".concat(newStatus === \"PUBLISHED\" ? \"published\" : \"unpublished\", \" successfully\")\n            });\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to \".concat(currentStatus === \"PUBLISHED\" ? \"unpublish\" : \"publish\", \" chapter\"),\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDelete = async ()=>{\n        try {\n            setIsDeleting(true);\n            // Call the API to delete novel\n            const response = await fetch(\"/api/novels/\".concat(novel.id), {\n                method: \"DELETE\"\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to delete novel\");\n            }\n            toast({\n                title: \"Success\",\n                description: \"Novel deleted successfully\"\n            });\n            // Redirect to dashboard\n            router.push(\"/dashboard\");\n        } catch (error) {\n            console.error(\"Error deleting novel:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete novel. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    const handleStatusChange = async (newStatus)=>{\n        try {\n            setIsUpdatingStatus(true);\n            // Call the API to update novel status\n            const response = await fetch(\"/api/novels/\".concat(novel.id), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    status: newStatus\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update novel status\");\n            }\n            toast({\n                title: \"Success\",\n                description: \"Novel \".concat(newStatus.toLowerCase(), \" successfully\")\n            });\n            // Refresh the page to show updated status\n            window.location.reload();\n        } catch (error) {\n            console.error(\"Error updating novel status:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to update novel status. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsUpdatingStatus(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-2xl\",\n                                                    children: novel.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    variant: novel.status === \"PUBLISHED\" ? \"default\" : \"secondary\",\n                                                    children: novel.status.toLowerCase()\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            className: \"text-base\",\n                                            children: novel.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                href: \"/dashboard/novels/\".concat(novel.id, \"/edit\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Edit Novel\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuContent, {\n                                                    align: \"end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                href: \"/novels/\".concat(novel.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                        lineNumber: 193,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"View Novel\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                href: \"/dashboard/novels/\".concat(novel.id, \"/edit\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                        lineNumber: 199,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Edit Details\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                href: \"/dashboard/novels/\".concat(novel.id, \"/credits\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                        lineNumber: 205,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Set Chapter Credits\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                href: \"/dashboard/novels/\".concat(novel.id, \"/monetization\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                        lineNumber: 211,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Advanced Monetization\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuSeparator, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        novel.status === \"DRAFT\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                            onClick: ()=>handleStatusChange(\"PUBLISHED\"),\n                                                            disabled: isUpdatingStatus,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Publish Novel\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 19\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                            onClick: ()=>handleStatusChange(\"DRAFT\"),\n                                                            disabled: isUpdatingStatus,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                    lineNumber: 229,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Unpublish Novel\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuSeparator, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialog, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                                        onSelect: (e)=>e.preventDefault(),\n                                                                        className: \"text-destructive focus:text-destructive\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                lineNumber: 240,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            \"Delete Novel\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                        lineNumber: 236,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogHeader, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogTitle, {\n                                                                                    children: \"Delete Novel\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                    lineNumber: 246,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogDescription, {\n                                                                                    children: [\n                                                                                        'Are you sure you want to delete \"',\n                                                                                        novel.title,\n                                                                                        '\"? This action cannot be undone. All chapters will also be deleted.'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                    lineNumber: 247,\n                                                                                    columnNumber: 23\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                            lineNumber: 245,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogFooter, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogCancel, {\n                                                                                    children: \"Cancel\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                    lineNumber: 253,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogAction, {\n                                                                                    onClick: handleDelete,\n                                                                                    disabled: isDeleting,\n                                                                                    className: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n                                                                                    children: isDeleting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_loading_spinner__WEBPACK_IMPORTED_MODULE_6__.LoadingSpinner, {\n                                                                                                className: \"mr-2 h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                                lineNumber: 261,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            \"Deleting...\"\n                                                                                        ]\n                                                                                    }, void 0, true) : \"Delete Novel\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                    lineNumber: 254,\n                                                                                    columnNumber: 23\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                            lineNumber: 252,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-5 w-5 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: ((_novel__count = novel._count) === null || _novel__count === void 0 ? void 0 : _novel__count.chapters) || ((_novel_chapters = novel.chapters) === null || _novel_chapters === void 0 ? void 0 : _novel_chapters.length) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: (((_novel__count1 = novel._count) === null || _novel__count1 === void 0 ? void 0 : _novel__count1.chapters) || ((_novel_chapters1 = novel.chapters) === null || _novel_chapters1 === void 0 ? void 0 : _novel_chapters1.length) || 0) === 1 ? \"Chapter\" : \"Chapters\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-5 w-5 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: novel.genre\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Genre\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-5 w-5 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.formatDate)(novel.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Created\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-5 w-5 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.formatDate)(novel.updatedAt)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Updated\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            children: \"Chapters\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: \"Manage your novel chapters\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        href: \"/dashboard/novels/\".concat(novel.id, \"/chapters/new\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Add Chapter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: !novel.chapters || novel.chapters.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto text-muted-foreground opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"No chapters yet\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Start writing by adding your first chapter\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        href: \"/dashboard/novels/\".concat(novel.id, \"/chapters/new\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Add First Chapter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                ...novel.chapters || []\n                            ].sort((a, b)=>a.order - b.order).map((chapter)=>{\n                                var _chapter_status;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                \"Chapter \",\n                                                                chapter.order,\n                                                                \": \",\n                                                                chapter.title\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: chapter.status === \"PUBLISHED\" ? \"default\" : \"secondary\",\n                                                            children: ((_chapter_status = chapter.status) === null || _chapter_status === void 0 ? void 0 : _chapter_status.toLowerCase()) || \"draft\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.formatDate)(chapter.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: chapter.status === \"PUBLISHED\" ? \"secondary\" : \"default\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>handlePublishChapter(chapter.id, chapter.status),\n                                                    children: chapter.status === \"PUBLISHED\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            \"Unpublish\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            \"Publish\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        href: \"/novels/\".concat(novel.id, \"/chapters/\").concat(chapter.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        href: \"/dashboard/novels/\".concat(novel.id, \"/chapters/\").concat(chapter.id, \"/edit\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Coins_DollarSign_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, chapter.id, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, this);\n}\n_s(NovelManagement, \"fDOl6im/ZBfc0jnHzKZxfka4g6k=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast,\n        _store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_12__.usePublishChapterMutation\n    ];\n});\n_c = NovelManagement;\nvar _c;\n$RefreshReg$(_c, \"NovelManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/novel/novel-management.tsx\n"));

/***/ })

});